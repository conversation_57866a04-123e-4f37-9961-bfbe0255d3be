package domain

import (
	"time"

	"hotel/common/money"
)

// CacheItem 缓存项
type CacheItem struct {
	MinPrice  *money.Money `json:"minPrice"`
	Available bool         `json:"available"`
	ExpireAt  int64        `json:"expireAt"`
	CreatedAt int64        `json:"createdAt"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	L1TTL        time.Duration // L1缓存TTL（内存）
	L2TTL        time.Duration // L2缓存TTL（Redis）
	MaxCacheSize int           // 最大缓存条目数
}

// DefaultCacheConfig 默认缓存配置
var DefaultCacheConfig = CacheConfig{
	L1TTL:        5 * time.Minute,
	L2TTL:        30 * time.Minute,
	MaxCacheSize: 10000,
}
