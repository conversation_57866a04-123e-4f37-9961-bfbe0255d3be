package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"hotel/common/log"
	"hotel/common/money"
	"hotel/common/utils"
	"hotel/search/domain"
	"hotel/search/protocol"
	"hotel/supplier"
	supplierDomain "hotel/supplier/domain"
)

// HotelPriceCacheService 酒店价格缓存服务
// 专注于为HotelList提供最低价格缓存，不新增接口
type HotelPriceCacheService struct {
	cache        interface{} // Redis缓存客户端
	supplierFact *supplier.Factory
	mu           sync.RWMutex
	memCache     map[string]*domain.CacheItem // L1内存缓存
}

// NewHotelPriceCacheService 创建价格缓存服务
func NewHotelPriceCacheService(cache interface{}, supplierFact *supplier.Factory) *HotelPriceCacheService {
	return &HotelPriceCacheService{
		cache:        cache,
		supplierFact: supplierFact,
		memCache:     make(map[string]*domain.CacheItem),
	}
}

// EnrichHotelsWithPrices 为酒店列表补充价格信息（使用缓存优化）
func (s *HotelPriceCacheService) EnrichHotelsWithPrices(ctx context.Context, req *protocol.HotelListReq, hotels []supplierDomain.SupplierHotel) []supplierDomain.SupplierHotel {
	if !req.RequireRooms || len(hotels) == 0 {
		return hotels
	}

	// 1. 尝试从缓存获取价格信息
	enrichedHotels := make([]supplierDomain.SupplierHotel, 0, len(hotels))
	missedHotels := make([]supplierDomain.SupplierHotel, 0)

	for _, hotel := range hotels {
		cacheKey := s.buildCacheKey(hotel.SupplierHotelId, req)

		// 先从L1缓存获取
		if cachedItem := s.getFromL1Cache(cacheKey); cachedItem != nil {
			hotel.HotelDynamicProfile.MinPrice = *cachedItem.MinPrice
			hotel.HotelDynamicProfile.IsAvailable = cachedItem.Available
			enrichedHotels = append(enrichedHotels, hotel)
			continue
		}

		// 再从L2缓存（Redis）获取
		if cachedItem := s.getFromL2Cache(ctx, cacheKey); cachedItem != nil {
			hotel.HotelDynamicProfile.MinPrice = *cachedItem.MinPrice
			hotel.HotelDynamicProfile.IsAvailable = cachedItem.Available
			// 更新L1缓存
			s.setL1Cache(cacheKey, cachedItem)
			enrichedHotels = append(enrichedHotels, hotel)
			continue
		}

		// 缓存未命中，需要实时查询
		missedHotels = append(missedHotels, hotel)
	}

	// 2. 对缓存未命中的酒店进行实时查询
	if len(missedHotels) > 0 {
		realTimeHotels := s.fetchRealTimePrices(ctx, req, missedHotels)
		enrichedHotels = append(enrichedHotels, realTimeHotels...)
	}

	log.Infoc(ctx, "EnrichHotelsWithPrices: total=%d, cached=%d, realtime=%d",
		len(hotels), len(hotels)-len(missedHotels), len(missedHotels))

	return enrichedHotels
}

// buildCacheKey 构建缓存键
func (s *HotelPriceCacheService) buildCacheKey(supplierHotelId string, req *protocol.HotelListReq) string {
	// 格式：hotel_price:{supplierHotelId}:{checkIn}:{checkOut}:{roomCount}:{adultCount}:{childrenCount}:{currency}
	return fmt.Sprintf("hotel_price:%s:%d:%d:%d:%d:%d:%s",
		supplierHotelId,
		req.CheckIn,
		req.CheckOut,
		req.RoomCount,
		req.AdultCount,
		req.ChildrenCount,
		req.Currency,
	)
}

// getFromL1Cache 从L1缓存获取
func (s *HotelPriceCacheService) getFromL1Cache(key string) *domain.CacheItem {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if item, exists := s.memCache[key]; exists && item.ExpireAt > time.Now().Unix() {
		return item
	}
	return nil
}

// getFromL2Cache 从L2缓存（Redis）获取
func (s *HotelPriceCacheService) getFromL2Cache(ctx context.Context, key string) *domain.CacheItem {
	value, err := s.getFromRedis(ctx, key)
	if err != nil || value == "" {
		return nil
	}

	item := utils.FromJSONP[domain.CacheItem](value)
	if item == nil || item.ExpireAt <= time.Now().Unix() {
		return nil
	}

	return item
}

// setL1Cache 设置L1缓存
func (s *HotelPriceCacheService) setL1Cache(key string, item *domain.CacheItem) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查缓存大小，如果超过限制则清理
	if len(s.memCache) >= domain.DefaultCacheConfig.MaxCacheSize {
		s.cleanupL1Cache()
	}

	s.memCache[key] = &domain.CacheItem{
		MinPrice:  item.MinPrice,
		Available: item.Available,
		ExpireAt:  time.Now().Add(domain.DefaultCacheConfig.L1TTL).Unix(),
		CreatedAt: time.Now().Unix(),
	}
}

// cleanupL1Cache 清理L1缓存（移除过期项）
func (s *HotelPriceCacheService) cleanupL1Cache() {
	now := time.Now().Unix()
	for key, item := range s.memCache {
		if item.ExpireAt <= now {
			delete(s.memCache, key)
		}
	}
}

// updateCache 更新缓存
func (s *HotelPriceCacheService) updateCache(ctx context.Context, key string, minPrice *money.Money, available bool) {
	item := &domain.CacheItem{
		MinPrice:  minPrice,
		Available: available,
		ExpireAt:  time.Now().Add(domain.DefaultCacheConfig.L2TTL).Unix(),
		CreatedAt: time.Now().Unix(),
	}

	// 更新L1缓存
	s.setL1Cache(key, item)

	// 更新L2缓存（Redis）
	jsonData := utils.ToJSON(item)
	if err := s.setToRedis(ctx, key, jsonData, int(domain.DefaultCacheConfig.L2TTL.Seconds())); err != nil {
		log.Errorc(ctx, "Failed to update Redis cache for key %s: %v", key, err)
	}
}

// fetchRealTimePrices 实时获取价格信息
func (s *HotelPriceCacheService) fetchRealTimePrices(ctx context.Context, req *protocol.HotelListReq, hotels []supplierDomain.SupplierHotel) []supplierDomain.SupplierHotel {
	result := make([]supplierDomain.SupplierHotel, 0, len(hotels))

	// 按供应商分组，利用供应商的批量查询能力
	supplierGroups := s.groupHotelsBySupplier(hotels)

	for supplier, hotelGroup := range supplierGroups {
		enrichedGroup := s.fetchPricesFromSupplier(ctx, req, supplier, hotelGroup)
		result = append(result, enrichedGroup...)
	}

	return result
}

// groupHotelsBySupplier 按供应商分组酒店
func (s *HotelPriceCacheService) groupHotelsBySupplier(hotels []supplierDomain.SupplierHotel) map[supplierDomain.Supplier][]supplierDomain.SupplierHotel {
	groups := make(map[supplierDomain.Supplier][]supplierDomain.SupplierHotel)

	for _, hotel := range hotels {
		groups[hotel.Supplier] = append(groups[hotel.Supplier], hotel)
	}

	return groups
}

// fetchPricesFromSupplier 从指定供应商获取价格
func (s *HotelPriceCacheService) fetchPricesFromSupplier(ctx context.Context, req *protocol.HotelListReq, supplier supplierDomain.Supplier, hotels []supplierDomain.SupplierHotel) []supplierDomain.SupplierHotel {
	// 对于支持HotelList返回价格的供应商（如TBO），直接调用HotelList
	if s.supportsHotelListWithPrices(supplier) {
		return s.fetchPricesViaHotelList(ctx, req, supplier, hotels)
	}

	// 对于支持批量HotelRates的供应商（如Dida），使用批量查询
	if s.supportsBatchHotelRates(supplier) {
		return s.fetchPricesViaBatchRates(ctx, req, supplier, hotels)
	}

	// 对于其他供应商，限制查询数量避免查订比问题
	return s.fetchPricesWithRateLimit(ctx, req, supplier, hotels)
}

// 辅助方法：检查供应商能力
func (s *HotelPriceCacheService) supportsHotelListWithPrices(supplier supplierDomain.Supplier) bool {
	// TBO的HotelList可能包含价格信息
	return supplier == supplierDomain.Supplier_TBO
}

func (s *HotelPriceCacheService) supportsBatchHotelRates(supplier supplierDomain.Supplier) bool {
	// Dida支持HotelIDList批量查询
	return supplier == supplierDomain.Supplier_Dida
}

// fetchPricesViaHotelList 通过HotelList获取价格（适用于TBO等）
func (s *HotelPriceCacheService) fetchPricesViaHotelList(ctx context.Context, req *protocol.HotelListReq, supplier supplierDomain.Supplier, hotels []supplierDomain.SupplierHotel) []supplierDomain.SupplierHotel {
	// TODO: 实现通过HotelList获取价格的逻辑
	log.Infoc(ctx, "fetchPricesViaHotelList for supplier %s, hotels: %d", supplier.String(), len(hotels))

	// 暂时返回原始酒店列表，实际实现中需要调用供应商的HotelList接口
	for i := range hotels {
		// 设置默认价格避免空值
		hotels[i].HotelDynamicProfile.MinPrice = money.Money{Amount: 0, Currency: req.Currency}
		hotels[i].HotelDynamicProfile.IsAvailable = false
	}

	return hotels
}

// fetchPricesViaBatchRates 通过批量HotelRates获取价格（适用于Dida等）
func (s *HotelPriceCacheService) fetchPricesViaBatchRates(ctx context.Context, req *protocol.HotelListReq, supplier supplierDomain.Supplier, hotels []supplierDomain.SupplierHotel) []supplierDomain.SupplierHotel {
	// TODO: 实现批量HotelRates查询逻辑
	log.Infoc(ctx, "fetchPricesViaBatchRates for supplier %s, hotels: %d", supplier.String(), len(hotels))

	// 暂时返回原始酒店列表
	for i := range hotels {
		hotels[i].HotelDynamicProfile.MinPrice = money.Money{Amount: 0, Currency: req.Currency}
		hotels[i].HotelDynamicProfile.IsAvailable = false
	}

	return hotels
}

// fetchPricesWithRateLimit 限流获取价格（避免查订比问题）
func (s *HotelPriceCacheService) fetchPricesWithRateLimit(ctx context.Context, req *protocol.HotelListReq, supplier supplierDomain.Supplier, hotels []supplierDomain.SupplierHotel) []supplierDomain.SupplierHotel {
	// 限制查询数量，避免对供应商造成压力
	maxQueries := 3
	if len(hotels) > maxQueries {
		log.Warnc(ctx, "Too many hotels (%d) for supplier %s, limiting to %d", len(hotels), supplier.String(), maxQueries)
		hotels = hotels[:maxQueries]
	}

	// TODO: 实现限流的单个HotelRates查询
	log.Infoc(ctx, "fetchPricesWithRateLimit for supplier %s, hotels: %d", supplier.String(), len(hotels))

	for i := range hotels {
		hotels[i].HotelDynamicProfile.MinPrice = money.Money{Amount: 0, Currency: req.Currency}
		hotels[i].HotelDynamicProfile.IsAvailable = false
	}

	return hotels
}

// Redis操作辅助方法
func (s *HotelPriceCacheService) getFromRedis(ctx context.Context, key string) (string, error) {
	if redisClient, ok := s.cache.(interface {
		GetCtx(ctx context.Context, key string) (string, error)
	}); ok {
		return redisClient.GetCtx(ctx, key)
	}
	return "", fmt.Errorf("redis client does not support GetCtx")
}

func (s *HotelPriceCacheService) setToRedis(ctx context.Context, key, value string, expireSeconds int) error {
	if redisClient, ok := s.cache.(interface {
		SetexCtx(ctx context.Context, key, value string, seconds int) error
	}); ok {
		return redisClient.SetexCtx(ctx, key, value, expireSeconds)
	}
	return fmt.Errorf("redis client does not support SetexCtx")
}

// ClearCache 清理缓存
func (s *HotelPriceCacheService) ClearCache(ctx context.Context, pattern string) error {
	// 清理L1缓存
	s.mu.Lock()
	if pattern == "*" {
		s.memCache = make(map[string]*domain.CacheItem)
	} else {
		for key := range s.memCache {
			if strings.Contains(key, pattern) {
				delete(s.memCache, key)
			}
		}
	}
	s.mu.Unlock()

	log.Infoc(ctx, "ClearCache called with pattern: %s", pattern)
	return nil
}
