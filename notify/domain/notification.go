package domain

import "time"

// 通知类型常量
const (
	NotificationTypeEmail = "email"
	NotificationTypeSMS   = "sms"
	NotificationTypePush  = "push"
)

// 通知状态常量
const (
	NotificationStatusPending = "pending"
	NotificationStatusSent    = "sent"
	NotificationStatusFailed  = "failed"
)

// 通知场景常量
const (
	ScenarioBookingConfirmation = "booking_confirmation"
	ScenarioBookingCancellation = "booking_cancellation"
	ScenarioPaymentFailure      = "payment_failure"
	ScenarioLowCredit          = "low_credit"
	ScenarioSystemReminder     = "system_reminder"
)

// SendNotificationRequest 发送通知请求
type SendNotificationRequest struct {
	Type      string                 `json:"type"`      // 通知类型：email, sms, push
	Scenario  string                 `json:"scenario"`  // 通知场景
	Recipient string                 `json:"recipient"` // 接收者
	Variables map[string]interface{} `json:"variables"` // 模板变量
}

// SendNotificationResponse 发送通知响应
type SendNotificationResponse struct {
	NotificationID int64  `json:"notificationId"`
	Status         string `json:"status"`
}

// EmailRequest 邮件请求
type EmailRequest struct {
	To          []string          `json:"to"`
	Subject     string            `json:"subject"`
	Body        string            `json:"body"`
	Attachments map[string][]byte `json:"attachments,omitempty"`
}

// SMSRequest 短信请求
type SMSRequest struct {
	To      string `json:"to"`
	Content string `json:"content"`
}

// PushRequest 推送请求
type PushRequest struct {
	To      string `json:"to"`
	Title   string `json:"title"`
	Content string `json:"content"`
}

// RenderedContent 渲染后的内容
type RenderedContent struct {
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

// GetHistoryRequest 获取历史请求
type GetHistoryRequest struct {
	Type      string    `json:"type,omitempty"`      // 通知类型过滤
	Scenario  string    `json:"scenario,omitempty"`  // 场景过滤
	Recipient string    `json:"recipient,omitempty"` // 接收者过滤
	Status    string    `json:"status,omitempty"`    // 状态过滤
	StartTime time.Time `json:"startTime,omitempty"` // 开始时间
	EndTime   time.Time `json:"endTime,omitempty"`   // 结束时间
	Page      int       `json:"page"`                 // 页码
	PageSize  int       `json:"pageSize"`            // 页大小
}

// GetHistoryResponse 获取历史响应
type GetHistoryResponse struct {
	Histories []*NotificationHistoryItem `json:"histories"`
	Total     int64                      `json:"total"`
	Page      int                        `json:"page"`
	PageSize  int                        `json:"pageSize"`
}

// NotificationHistoryItem 通知历史项
type NotificationHistoryItem struct {
	ID             int64     `json:"id"`
	NotificationID int64     `json:"notificationId"`
	Type           string    `json:"type"`
	Scenario       string    `json:"scenario"`
	Recipient      string    `json:"recipient"`
	Subject        string    `json:"subject"`
	Content        string    `json:"content"`
	Status         string    `json:"status"`
	ErrorMessage   string    `json:"errorMessage,omitempty"`
	SentAt         time.Time `json:"sentAt"`
	CreatedAt      time.Time `json:"createdAt"`
}

// NotificationHistoryRequest 通知历史请求
type NotificationHistoryRequest struct {
	Type      string `json:"type"`      // 通知类型过滤
	Scenario  string `json:"scenario"`  // 场景过滤
	Status    string `json:"status"`    // 状态过滤
	Recipient string `json:"recipient"` // 接收者过滤
	Page      int    `json:"page"`
	PageSize  int    `json:"pageSize"`
}

// NotificationHistoryResponse 通知历史响应
type NotificationHistoryResponse struct {
	Histories []*NotificationHistoryItem `json:"histories"`
	Total     int64                       `json:"total"`
	Page      int                         `json:"page"`
	PageSize  int                         `json:"pageSize"`
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Type        string `json:"type" binding:"required"`        // 通知类型
	Scenario    string `json:"scenario" binding:"required"`    // 使用场景
	Name        string `json:"name" binding:"required"`        // 模板名称
	Description string `json:"description"`                    // 模板描述
	Subject     string `json:"subject"`                        // 主题（邮件用）
	Content     string `json:"content" binding:"required"`     // 模板内容
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Subject     string `json:"subject"`
	Content     string `json:"content"`
}

// TemplateListRequest 模板列表请求
type TemplateListRequest struct {
	Type     string `json:"type"`     // 通知类型过滤
	Scenario string `json:"scenario"` // 场景过滤
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
}

// TemplateListResponse 模板列表响应
type TemplateListResponse struct {
	Templates []*TemplateItem `json:"templates"`
	Total     int64           `json:"total"`
	Page      int             `json:"page"`
	PageSize  int             `json:"pageSize"`
}

// TemplateItem 模板项
type TemplateItem struct {
	ID          int64     `json:"id"`
	Type        string    `json:"type"`        // 通知类型
	Scenario    string    `json:"scenario"`    // 使用场景
	Name        string    `json:"name"`        // 模板名称
	Description string    `json:"description"` // 模板描述
	Subject     string    `json:"subject"`     // 主题（邮件用）
	Content     string    `json:"content"`     // 模板内容
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}
