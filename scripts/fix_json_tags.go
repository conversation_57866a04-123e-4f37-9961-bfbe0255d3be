package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// underscoreToCamelCase 将下划线命名转换为驼峰命名
func underscoreToCamelCase(s string) string {
	// 将字符串按下划线分割
	parts := strings.Split(s, "_")
	if len(parts) <= 1 {
		return s
	}
	
	// 第一个部分保持小写，后续部分首字母大写
	result := parts[0]
	for i := 1; i < len(parts); i++ {
		if len(parts[i]) > 0 {
			result += strings.ToUpper(parts[i][:1]) + parts[i][1:]
		}
	}
	return result
}

// processFile 处理单个文件
func processFile(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	modified := false

	// JSON tag的正则表达式，匹配包含下划线的JSON tag
	jsonTagRegex := regexp.MustCompile(`json:"([^"]*_[^"]*)"`)
	
	for scanner.Scan() {
		line := scanner.Text()
		originalLine := line
		
		// 跳过注释行
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, "//") {
			lines = append(lines, line)
			continue
		}
		
		// 查找并替换JSON tag
		matches := jsonTagRegex.FindAllStringSubmatch(line, -1)
		for _, match := range matches {
			if len(match) >= 2 {
				originalTag := match[1]
				
				// 检查是否包含逗号，分离字段名和选项
				parts := strings.Split(originalTag, ",")
				fieldName := parts[0]
				
				// 只有当字段名包含下划线时才进行转换
				if strings.Contains(fieldName, "_") {
					camelCaseField := underscoreToCamelCase(fieldName)
					
					// 重构新的tag
					var newTag string
					if len(parts) > 1 {
						// 保留omitempty等选项
						newTag = camelCaseField + "," + strings.Join(parts[1:], ",")
					} else {
						newTag = camelCaseField
					}
					
					// 替换整个JSON tag
					oldJsonTag := fmt.Sprintf(`json:"%s"`, originalTag)
					newJsonTag := fmt.Sprintf(`json:"%s"`, newTag)
					line = strings.Replace(line, oldJsonTag, newJsonTag, -1)
					
					if line != originalLine {
						modified = true
						fmt.Printf("  %s: %s -> %s\n", filepath.Base(filePath), originalTag, newTag)
					}
				}
			}
		}
		
		lines = append(lines, line)
	}

	if err := scanner.Err(); err != nil {
		return err
	}

	// 如果文件被修改，写回文件
	if modified {
		return writeLinesToFile(filePath, lines)
	}

	return nil
}

// writeLinesToFile 将行写回文件
func writeLinesToFile(filePath string, lines []string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	for _, line := range lines {
		if _, err := writer.WriteString(line + "\n"); err != nil {
			return err
		}
	}
	return writer.Flush()
}

// walkGoFiles 遍历所有Go文件
func walkGoFiles(rootDir string, processFunc func(string) error) error {
	return filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.go文件
		if strings.HasSuffix(path, ".go") {
			// 跳过vendor目录
			if strings.Contains(path, "/vendor/") {
				return nil
			}
			
			return processFunc(path)
		}

		return nil
	})
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run fix_json_tags.go <project_root_directory>")
		os.Exit(1)
	}

	rootDir := os.Args[1]
	
	fmt.Printf("开始处理项目目录: %s\n", rootDir)
	fmt.Println("正在查找并修复JSON tag中的下划线命名...")
	fmt.Println()

	processedFiles := 0
	modifiedFiles := 0

	err := walkGoFiles(rootDir, func(filePath string) error {
		processedFiles++
		
		// 检查文件是否需要修改
		file, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer file.Close()

		jsonTagRegex := regexp.MustCompile(`json:"([^"]*_[^"]*)"`)
		scanner := bufio.NewScanner(file)
		needsModification := false

		for scanner.Scan() {
			line := scanner.Text()
			trimmedLine := strings.TrimSpace(line)
			
			// 跳过注释行
			if strings.HasPrefix(trimmedLine, "//") {
				continue
			}
			
			if jsonTagRegex.MatchString(line) {
				needsModification = true
				break
			}
		}

		if needsModification {
			fmt.Printf("处理文件: %s\n", filePath)
			modifiedFiles++
			return processFile(filePath)
		}

		return nil
	})

	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}

	fmt.Println()
	fmt.Printf("处理完成！\n")
	fmt.Printf("总共检查文件: %d\n", processedFiles)
	fmt.Printf("修改文件数量: %d\n", modifiedFiles)
}