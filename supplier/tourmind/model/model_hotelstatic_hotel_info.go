/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelstaticHotelInfo struct for HotelstaticHotelInfo
type HotelstaticHotelInfo struct {
	// Hotel address.
	Address string `json:"Address,omitempty"`
	// Hotel address in Chinese.
	AddressCN string `json:"AddressCN,omitempty"`
	// City code.,Matched region ID.
	CityCode string `json:"CityCode,omitempty"`
	// City name.
	CityName string `json:"CityName,omitempty"`
	// City name. 中文
	CityNameCN string `json:"CityNameCN,omitempty"`
	// Country code.，ISO 3166-1 alpha-2, e.g., China: CN.
	CountryCode string `json:"CountryCode,omitempty"`
	// TourMind hotel ID.
	HotelId string `json:"HotelId,omitempty"`
	Images []HotelstaticImage `json:"Images,omitempty"`
	// Latitude.
	Latitude string `json:"Latitude,omitempty"`
	// Longitude.
	Longitude string `json:"Longitude,omitempty"`
	// Hotel name.
	Name string `json:"Name,omitempty"`
	// Hotel name in Chinese.
	NameCN string `json:"NameCN,omitempty"`
	// Hotel phone number.
	Phone string `json:"Phone,omitempty"`
	// Hotel star rating.
	StarRating string `json:"StarRating,omitempty"`
}
