package netstorming

import (
	"context"
	"hotel/common/log"
	"hotel/common/utils"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"
	"hotel/supplier/netstorming/model"

	pkgerr "github.com/pkg/errors"

	"github.com/spf13/cast"
)

type NetstormingClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func (s *NetstormingClient) Supplier() domain.Supplier {
	return domain.Supplier_Netstorming
}

func (s *NetstormingClient) HotelIdList(ctx context.Context, req *domain.HotelIdListReq) (*domain.HotelIdListResp, error) {

	//TODO: API暂时无法通过城市code获取酒店列表，需要通过酒店id列表获取
	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, err
	}

	const (
		batchSize = 100   // 每次拉取100个酒店
		maxHotels = 50000 // 最多拉取50000个酒店
	)

	var allSupplierHotelIds []string
	startFrom := int64(0)

	for {
		nsReq := model.HotelSearchRequest{
			Header: model.Header{
				Properties: *properties,
				Version:    "1.7.1",
				Timestamp:  genTimestamp(),
			},
			Query: model.HotelSearchQuery{
				Type:      "get_inventory",
				Product:   "hotel",
				StartFrom: startFrom,
				BatchSize: batchSize,
			},
		}

		nsResp := model.HotelSearchResponse{}

		err = s.execute(ctx, domain.APIName_HotelIdList, &nsReq, &nsResp)
		if err != nil {
			return nil, err
		}

		// 提取当前批次的酒店ID
		batchHotelIds, _ := utils.Map(nsResp.Result.Hotels.Hotels, func(hotel model.StaticHotel) (string, error) {
			return cast.ToStringE(hotel.ID)
		})

		// 将当前批次的酒店ID添加到总列表中
		allSupplierHotelIds = append(allSupplierHotelIds, batchHotelIds...)

		// 检查是否已经拉取完所有酒店或达到最大限制
		hotels := nsResp.Result.Hotels
		if hotels.To >= hotels.Total || len(allSupplierHotelIds) >= maxHotels {
			break
		}

		// 如果已经拉取了足够的酒店，截断并退出
		if len(allSupplierHotelIds) >= maxHotels {
			allSupplierHotelIds = allSupplierHotelIds[:maxHotels]
			break
		}

		// 更新下一次拉取的起始位置
		startFrom = int64(hotels.To)
	}

	return &domain.HotelIdListResp{
		SupplierHotelIds: allSupplierHotelIds,
	}, nil
}

func (s *NetstormingClient) HotelStaticDetail(ctx context.Context, req *domain.HotelStaticDetailReq) (*domain.HotelStaticDetailResp, error) {

	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, err
	}

	now := genTimestamp()

	nsReq := model.HotelDetailRequest{
		Header: model.Header{
			Properties: *properties,
			Version:    "1.7.1",
			Timestamp:  now,
		},
		Query: model.HotelDetailQuery{
			Type:    "details",
			Product: "hotel",
			HotelId: model.HotelId{
				Id: req.SupplierHotelId,
			},
		},
	}

	nsResp := model.HotelDetailResponse{}

	err = s.execute(ctx, domain.APIName_HotelStaticDetail, &nsReq, &nsResp)
	if err != nil {
		return nil, err
	}

	staticInfo := nsResp.Result.ToDomain()

	return &domain.HotelStaticDetailResp{
		HotelStaticProfile: staticInfo,
	}, nil
}

func (s *NetstormingClient) HotelList(ctx context.Context, req *domain.HotelListReq) (*domain.HotelListResp, error) {
	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, err
	}

	now := genTimestamp()

	var resp domain.HotelListResp

	for _, supplierHotelId := range req.SupplierHotelIds {
		nsReq := model.HotelDetailRequest{
			Header: model.Header{
				Properties: *properties,
				Version:    "1.7.1",
				Timestamp:  now,
			},
			Query: model.HotelDetailQuery{
				Type:    "details",
				Product: "hotel",
				HotelId: model.HotelId{
					Id: supplierHotelId,
				},
			},
		}

		nsResp := model.HotelDetailResponse{}

		err = s.execute(ctx, domain.APIName_HotelStaticDetail, &nsReq, &nsResp)
		if err != nil {
			return nil, err
		}

		staticProfile := nsResp.Result.ToDomain()

		resp.Hotels = append(resp.Hotels, domain.SupplierHotel{
			Name:               staticProfile.Name,
			SupplierHotelId:    supplierHotelId,
			HotelStaticProfile: staticProfile,
		})
	}

	return &resp, nil
}

func (s *NetstormingClient) HotelRates(ctx context.Context, req *domain.HotelRatesReq) (*domain.HotelRatesResp, error) {
	ctxPl, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	properties, err := s.GetProperties(ctxPl.Properties)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}

	now := genTimestamp()

	nsReq := model.CheckAvailReq{
		Header: model.Header{
			Properties: *properties,
			Version:    "1.7.1",
			Timestamp:  now,
		},
		Query: model.AvailbilityQuery{
			Type:        "availability",
			Product:     "hotel",
			Nationality: req.NationalityCode,
			CheckIn:     model.Checkin{Date: req.CheckIn.Format("2006-01-02")},
			CheckOut:    model.Checkout{Date: req.CheckOut.Format("2006-01-02")},
			Hotels: []model.HotelId{
				{
					Id: req.SupplierHotelId,
				},
			},
			Details:  []model.RoomDetail{genRoomDetail(req.GuestRoomOption, "")},
			Filters:  []string{"AVAILONLY", "AVLHEAVY"},
			Currency: req.Currency,
			Timeout:  20,
		},
	}

	nsResp := model.AvailabilityRes{}

	err = s.execute(ctx, domain.APIName_HotelRates, &nsReq, &nsResp)
	if err != nil {
		return nil, err
	}

	resp, err := convertRoomRatesResponse(ctx, &nsResp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *NetstormingClient) CheckAvail(ctx context.Context, req *domain.CheckAvailReq) (*domain.CheckAvailResp, error) {
	ctxPl, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	properties, err := s.GetProperties(ctxPl.Properties)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}
	ratesReq, err := domain.GetHotelRateReqFromSession(ctx, ctxPl.Session)
	if err != nil {
		log.Errorc(ctx, "Failed to get hotel rate req from session: %v", err)
		return nil, pkgerr.Wrap(err, "failed to get hotel rate req from session")
	}

	now := genTimestamp()

	nsReq := model.CheckAvailReq{
		Header: model.Header{
			Properties: *properties,
			Version:    "1.7.1",
			Timestamp:  now,
		},
		Query: model.AvailbilityQuery{
			Type:     "availability",
			Product:  "hotel",
			CheckIn:  model.Checkin{Date: ratesReq.CheckIn.Format("2006-01-02")},
			CheckOut: model.Checkout{Date: ratesReq.CheckOut.Format("2006-01-02")},
			Hotels: []model.HotelId{
				{
					Id: ratesReq.SupplierHotelId,
				},
			},
			Details: []model.RoomDetail{genRoomDetail(ratesReq.GuestRoomOption, ctxPl.GetSessionParams(ctx, "roomType"))},
			Search: &model.AvailabilitySearch{
				Number:    cast.ToString(ctxPl.GetSessionParams(ctx, "number")),
				Agreement: ctxPl.GetSessionParams(ctx, "agreement"),
				Price:     cast.ToFloat64(ctxPl.GetSessionParams(ctx, "price")),
			},
		},
	}

	nsResp := model.CheckAvailRes{}

	err = s.execute(ctx, domain.APIName_CheckAvail, &nsReq, &nsResp)
	if err != nil {
		return nil, err
	}

	resp, err := convertAvailResponse(ctx, &nsResp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
func (s *NetstormingClient) Book(ctx context.Context, req *domain.BookReq) (*domain.BookResp, error) {
	ctxPl, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	properties, err := s.GetProperties(ctxPl.Properties)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}
	ratesReq, err := domain.GetHotelRateReqFromSession(ctx, ctxPl.Session)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get hotel rate req from session")
	}

	now := genTimestamp()

	// 使用新的房间生成函数
	rooms := genRoomsFromBookReq(req, ctxPl.GetSessionParams(ctx, "roomType"))

	nsReq := model.BookRequest{
		Header: model.Header{
			Properties: *properties,
			Version:    "1.7.1",
			Timestamp:  now,
		},
		Query: model.BookQuery{
			Type:    "book",
			Product: "hotel",
			Search: &model.Search{
				Number: cast.ToInt(ctxPl.GetSessionParams(ctx, "number")),
			},
			Synchronous: &model.Synchronous{
				Value: true,
			},
			Nationality: ratesReq.NationalityCode,
			Checkin: &model.Checkin{
				Date: ratesReq.CheckIn.Format("2006-01-02"),
			},
			Checkout: &model.Checkout{
				Date: ratesReq.CheckOut.Format("2006-01-02"),
			},
			AvailOnly: &model.AvailOnly{
				Value: true,
			},
			Hotel: &model.Hotel{
				Code:      ratesReq.SupplierHotelId,
				Agreement: ctxPl.GetSessionParams(ctx, "agreement"),
			},
			Reference: model.Reference{
				Code: cast.ToString(req.PlatformOrderId),
			},
			Currency: ratesReq.Currency,
			Details: &model.Details{
				Rooms: rooms,
			},
			Remarks: &model.Remarks{
				// 这里需要根据req.RemarkToHotel生成remark
			},
		},
	}

	nsResp := model.BookResponse{}

	err = s.execute(ctx, domain.APIName_Book, &nsReq, &nsResp)
	if err != nil {
		return nil, err
	}

	resp, err := convertBookResponse(ctx, &nsResp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
func (s *NetstormingClient) QueryOrderByIDs(ctx context.Context, req *domain.QueryOrdersReq) (*domain.QueryOrdersResp, error) {

	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, err
	}

	nsReq := model.QueryOrderRequest{
		Header: model.Header{
			Properties: *properties,
			Version:    "1.7.1",
			Timestamp:  genTimestamp(),
		},
		Query: model.QueryOrderQuery{
			Type:    "track",
			Product: "hotel",
			Reference: model.QueryOrderReference{
				Code: cast.ToString(req.PlatformOrderId),
			},
		},
	}

	nsResp := model.QueryOrderResponse{}

	err = s.execute(ctx, domain.APIName_QueryOrder, &nsReq, &nsResp)
	if err != nil {
		return nil, err
	}

	resp, err := convertQueryOrderResponse(ctx, &nsResp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
func (s *NetstormingClient) Cancel(ctx context.Context, req *domain.CancelReq) (*domain.CancelResp, error) {
	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, err
	}

	nsReq := model.CancelRequest{
		Header: model.Header{
			Properties: *properties,
			Version:    "1.7.1",
			Timestamp:  genTimestamp(),
		},
		Query: model.CancelQuery{
			Type:    "cancel",
			Product: "hotel",
			Booking: model.Booking{
				Name: req.SupplierOrderId,
			},
		},
	}

	nsResp := model.BookResponse{}

	err = s.execute(ctx, domain.APIName_Cancel, &nsReq, &nsResp)
	if err != nil {
		return nil, err
	}

	resp, err := convertCancelResponse(ctx, &nsResp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
