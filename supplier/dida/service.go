package dida

import (
	"context"
	"errors"
	"strconv"
	"time"

	pkgerr "github.com/pkg/errors"

	"github.com/spf13/cast"

	biDomain "hotel/bi/domain"
	"hotel/common/utils"
	"hotel/supplier/dida/model"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"

	"github.com/zeromicro/go-zero/core/logx"
)

type DidaClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func (s *DidaClient) Supplier() domain.Supplier {
	return domain.Supplier_Dida
}

func (s *DidaClient) batchHotelStaticDetail(ctx context.Context, supplierHotelIds []int64) (*domain.BatchHotelStaticDetailResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	didaReq := &model.HotelDetailsRequest{
		Language: prop.GetLanguage(),
		HotelIDS: supplierHotelIds,
	}
	didaResp := new(model.HotelDetailsResponse)
	err := s.execute(ctx, domain.APIName_HotelStaticDetail, didaReq, didaResp)
	if err != nil {
		return nil, err
	}

	convertedResult := convertHotelDetailsResponse2HotelListResp(didaResp)
	return &domain.BatchHotelStaticDetailResp{
		Hotels: convertedResult.Hotels,
	}, nil
}
func (s *DidaClient) HotelStaticDetail(ctx context.Context, req *domain.HotelStaticDetailReq) (*domain.HotelStaticDetailResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	didaReq := &model.HotelDetailsRequest{
		Language: prop.GetLanguage(),
		HotelIDS: []int64{cast.ToInt64(req.SupplierHotelId)},
	}
	didaResp := new(model.HotelDetailsResponse)
	err := s.execute(ctx, domain.APIName_HotelStaticDetail, didaReq, didaResp)
	if err != nil {
		return nil, err
	}

	convertedResult := convertHotelDetailsResponse2HotelListResp(didaResp)
	return &domain.HotelStaticDetailResp{
		HotelStaticProfile: convertedResult.Hotels[0].HotelStaticProfile,
	}, nil
}

func (s *DidaClient) HotelIdList(ctx context.Context, req *domain.HotelIdListReq) (*domain.HotelIdListResp, error) {
	hotelLists, err := s.getHotelListByCountry(ctx, req)
	if err != nil {
		return nil, err
	}
	hotelIDs, _ := utils.Map(hotelLists.Data, func(id int64) (string, error) {
		return cast.ToStringE(id)
	})
	return &domain.HotelIdListResp{SupplierHotelIds: hotelIDs}, nil
}

func (s *DidaClient) HotelList(ctx context.Context, req *domain.HotelListReq) (*domain.HotelListResp, error) {
	var supplierHotelIds []int64
	if len(req.SupplierHotelIds) == 0 {
		hotelLists, err := s.getHotelListByCountry(ctx, &domain.HotelIdListReq{
			CountryCode: req.CountryOption.CountryCode,
		})
		if err != nil {
			return nil, err
		}
		supplierHotelIds = hotelLists.Data
	} else {
		supplierHotelIds, _ = utils.Map(req.SupplierHotelIds, func(id string) (int64, error) {
			return cast.ToInt64E(id)
		})
	}

	hotelDetails, err := s.batchHotelStaticDetail(ctx, supplierHotelIds)
	if err != nil {
		return nil, err
	}

	return &domain.HotelListResp{
		Hotels: hotelDetails.Hotels,
	}, nil

}

// HotelRates 获取酒店房型和价格
func (s *DidaClient) HotelRates(ctx context.Context, req *domain.HotelRatesReq) (*domain.HotelRatesResp, error) {
	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}
	rateSearchReq := &model.SearchRateRequest{
		Header:       properties.Header,
		HotelIDList:  []int64{cast.ToInt64(req.SupplierHotelId)},
		CheckInDate:  req.CheckIn.Format("2006-01-02"),
		CheckOutDate: req.CheckOut.Format("2006-01-02"),
		IsRealTime: model.IsRealTime{
			Value:     true,
			RoomCount: req.RoomCount,
		},
		RealTimeOccupancy: model.RealTimeOccupancy{
			AdultCount:      req.AdultCount,
			ChildCount:      req.ChildrenCount,
			ChildAgeDetails: convertChildAgeDetails(req.GuestRoomOption),
		},
		Currency:    req.Currency,
		Nationality: req.NationalityCode,
	}

	var rateSearchResp = new(model.SearchRateResponse)
	err = s.execute(ctx, domain.APIName_HotelRates, rateSearchReq, &rateSearchResp)
	if err != nil {
		return nil, err
	}

	if rateSearchResp.Error != nil {
		return nil, errors.New(rateSearchResp.Error.Message)
	}

	hotelRatePlanResp, err := convertSearchRateResponseToDomainHotelRatesResp(ctx, rateSearchResp)
	if err != nil {
		return nil, err
	}
	return hotelRatePlanResp, nil
}

func convertChildAgeDetails(ratesReq domain.GuestRoomOption) (out []int64) {
	for _, v := range ratesReq.Guests {
		if v.Age < 12 { // 供应上决定
			out = append(out, v.Age)
		}
	}
	return out
}

func convertCheckAvailOccupancy(ratesReq domain.GuestRoomOption) (out []model.OccupancyDetail) {
	return []model.OccupancyDetail{
		{
			ChildCount:      ratesReq.ChildrenCount,
			AdultCount:      ratesReq.AdultCount,
			RoomNum:         1, // 房间索引，从1开始
			ChildAgeDetails: convertChildAgeDetails(ratesReq),
		},
	}
}

// CheckAvail .
func (s *DidaClient) CheckAvail(ctx context.Context, req *domain.CheckAvailReq) (*domain.CheckAvailResp, error) {
	ctxPl, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	properties, err := s.GetProperties(ctxPl.Properties)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}
	ratesReq, err := domain.GetHotelRateReqFromSession(ctx, ctxPl.Session)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get hotel rate req from session")
	}

	ratePlanId := req.RatePkgId
	// 从 session 中获取并设置 metadata
	availReq := &model.CheckAvailRequest{
		PreBook:          true, // return reference_no only if PreBook=true
		CheckInDate:      ratesReq.CheckIn.Format("2006-01-02"),
		CheckOutDate:     ratesReq.CheckOut.Format("2006-01-02"),
		NumOfRooms:       ratesReq.RoomCount,
		HotelID:          cast.ToInt64(ratesReq.SupplierHotelId),
		Header:           properties.Header,
		OccupancyDetails: convertCheckAvailOccupancy(ratesReq.GuestRoomOption),
		Currency:         ratesReq.Currency,
		Nationality:      ratesReq.NationalityCode,
		RatePlanID:       ratePlanId,
		IsNeedOnRequest:  false,
		Metadata:         ctxPl.GetSessionParams(ctx, getMetadataKey(ratePlanId)),
	}

	logx.WithContext(ctx).Infof("Retrieved metadata for RatePlanID %s: %s", req.RatePkgId, availReq.Metadata)

	// 添加调试日志
	availReqJSON := utils.LogMarsh(availReq)
	logx.WithContext(ctx).Infof("CheckAvail request: %s", availReqJSON)

	var availResp model.CheckAvailResponse
	err = s.execute(ctx, domain.APIName_CheckAvail, availReq, &availResp)
	if err != nil {
		return nil, err
	}

	if availResp.Error != nil {
		return nil, errors.New(availResp.Error.Message)
	}

	roomRatePkg, err := availResp.ToDomain()
	if err != nil {
		return nil, err
	}
	refNo := availResp.Success.PriceDetails.ReferenceNo
	// 保存 reference_no 到 session
	ctxPl.UpdateSessionParams(ctx, "reference_no", refNo)
	apiOut := biDomain.HBLogGet(ctx).APIOut
	apiOut.BizType = biDomain.BizType_HotelRatePkg
	apiOut.BizId = refNo

	return &domain.CheckAvailResp{
		Status:      domain.CheckAvailStatusAvailable,
		RoomRatePkg: roomRatePkg,
	}, nil
}

func convertContact(r domain.Booker) model.Contact {
	return model.Contact{
		Name: model.Name{
			First: r.FirstName,
			Last:  r.LastName,
		},
		Email: r.Email,
		Phone: r.Phone.String(),
	}
}
func convertGuest(r domain.Guest) model.GuestInfo {
	return model.GuestInfo{
		IsAdult: r.Age == 0 || r.Age >= 12,
		Name: model.Name{
			Last:  r.LastName,
			First: r.FirstName,
		},
	}
}

func convertGuestList(in []domain.Guest) (out []model.GuestList) {
	roomMap := make(map[int64][]domain.Guest)

	for _, v := range in {
		if v.RoomIndex == 0 {
			v.RoomIndex = 1
		}
		roomMap[v.RoomIndex] = append(roomMap[v.RoomIndex], v)
	}
	for ridx, room := range roomMap {
		var guests []model.GuestInfo
		for _, r := range room {
			guests = append(guests, convertGuest(r))
		}
		out = append(out, model.GuestList{
			RoomNum:   ridx,
			GuestInfo: guests,
		})
	}
	return out
}

// Book .
func (s *DidaClient) Book(ctx context.Context, req *domain.BookReq) (*domain.BookResp, error) {
	ctxPl, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	properties, err := s.GetProperties(ctxPl.Properties)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}
	ratesReq, err := domain.GetHotelRateReqFromSession(ctx, ctxPl.Session)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get hotel rate req from session")
	}
	bookingReq := &model.BookingRequest{
		Header:          properties.Header,
		CheckInDate:     ratesReq.CheckIn.Format("2006-01-02"),
		CheckOutDate:    ratesReq.CheckOut.Format("2006-01-02"),
		NumOfRooms:      ratesReq.RoomCount,
		GuestList:       convertGuestList(req.Guests),
		Contact:         convertContact(req.Booker),
		ClientReference: cast.ToString(req.PlatformOrderId),
		ReferenceNo:     ctxPl.GetSessionParams(ctx, "reference_no"),
		RatePlanID:      req.RatePkgId,
	}

	var bookingResp model.BookingResponse
	err = s.execute(ctx, domain.APIName_Book, bookingReq, &bookingResp)
	if err != nil {
		return nil, err
	}

	if bookingResp.Error != nil {
		return nil, errors.New(bookingResp.Error.Message)
	}

	var orderId string
	var orderStatus domain.OrderStatus
	if bookingResp.Success != nil {
		orderId = bookingResp.Success.BookingDetails.BookingID
		statusStr := bookingResp.Success.BookingDetails.Status
		orderStatus = model.ConvertDidaOrderStatusToDomain(statusStr)
	}
	return &domain.BookResp{
		SupplierOrderId: orderId,
		OrderStatus:     orderStatus,
	}, nil
}

// QueryOrderByIDs 获取订单列表
func (s *DidaClient) QueryOrderByIDs(ctx context.Context, req *domain.QueryOrdersReq) (*domain.QueryOrdersResp, error) {
	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}

	queryReq := model.QueryRequest{
		Header: properties.Header,
		SearchBy: model.SearchBy{
			BookingInfo: model.BookingInfo{
				ClientReference: cast.ToString(req.PlatformOrderId),
			},
		},
	}

	var queryResp model.QueryResponse
	err = s.execute(ctx, domain.APIName_QueryOrder, queryReq, &queryResp)
	if err != nil {
		return nil, err
	}

	if queryResp.Error != nil {
		return nil, errors.New(queryResp.Error.Message)
	}

	orders, err := queryResp.ToDomain()
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// Cancel 取消订单
func (s *DidaClient) Cancel(ctx context.Context, req *domain.CancelReq) (*domain.CancelResp, error) {
	properties, err := s.GetProperties(ctx)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get properties")
	}
	preCancelReq := model.PreCancelRequest{
		Header:    properties.Header,
		BookingID: req.SupplierOrderId,
	}

	var preCancelResp model.PreCancelResponse
	err = s.execute(ctx, "PreCancel", preCancelReq, &preCancelResp)
	if err != nil {
		return nil, err
	}

	if preCancelResp.Error != nil {
		return nil, errors.New(preCancelResp.Error.Message)
	}

	if preCancelResp.Success.BookingID == "" {
		return nil, errors.New("PreCancel response BookingID is empty, cannot proceed to CancelOrder")
	}

	cancelReq := &model.CancelRequest{
		Header:    properties.Header,
		BookingID: preCancelResp.Success.BookingID,
		ConfirmID: preCancelResp.Success.ConfirmID,
	}

	// 预取消成功后，执行实际取消
	var cancelResp model.CancelResponse
	err = s.execute(ctx, domain.APIName_Cancel, cancelReq, &cancelResp)
	if err != nil {
		return nil, err
	}

	if cancelResp.Error != nil {
		return nil, errors.New(cancelResp.Error.Message)
	}

	return &domain.CancelResp{
		Status: domain.OrderStatus_Cancelled,
	}, nil
}
func (s *DidaClient) getHotelListByCountry(ctx context.Context, req *domain.HotelIdListReq) (*model.HotelListResponse, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	queryParams := map[string]string{
		"countryCode":    req.CountryCode,
		"lastUpdataTime": strconv.FormatInt(time.Now().Unix(), 10),
		"language":       prop.GetLanguage(),
	}

	var res = new(model.HotelListResponse)
	err := s.execute(ctx, domain.APIName_HotelList, queryParams, &res)
	if err != nil {
		return nil, err
	}

	return res, nil
}
