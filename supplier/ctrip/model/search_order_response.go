package model

import (
	"encoding/json"
	"strconv"

	"github.com/zeromicro/go-zero/core/jsonx"
)

type SearchOrderResp struct {
	ItineraryList  []Itinerary       `json:"ItineraryList"`
	Status         QueryResultStatus `json:"Status"`
	ResponseStatus ResponseStatus    `json:"ResponseStatus"`
}

type ClientInfo struct {
	RoomIndex              int     `json:"RoomIndex"`
	ClientName             string  `json:"ClientName"`
	EmployeeID             string  `json:"EmployeeID"`
	CostCenter1            string  `json:"CostCenter1"`
	CostCenter2            string  `json:"CostCenter2"`
	CostCenter3            string  `json:"CostCenter3"`
	CostCenter4            string  `json:"CostCenter4"`
	CostCenter5            string  `json:"CostCenter5"`
	CostCenter6            string  `json:"CostCenter6"`
	Dept1                  string  `json:"Dept1"`
	Dept2                  string  `json:"Dept2"`
	Dept3                  string  `json:"Dept3"`
	Dept4                  string  `json:"Dept4"`
	Dept5                  string  `json:"Dept5"`
	Dept6                  string  `json:"Dept6"`
	Dept7                  string  `json:"Dept7"`
	Dept8                  string  `json:"Dept8"`
	Dept9                  string  `json:"Dept9"`
	Dept10                 string  `json:"Dept10"`
	CheckinTime            string  `json:"CheckinTime"`
	CheckoutTime           string  `json:"CheckoutTime"`
	ActualDepartureTime    string  `json:"ActualDepartureTime"`
	ShareOrderAmount       float64 `json:"ShareOrderAmount"`
	ClientPreApprovalNo    string  `json:"ClientPreApprovalNo"`
	Price                  float64 `json:"Price"`
	Star                   string  `json:"Star"`
	ActualUpperAmount      float64 `json:"ActualUpperAmount"`
	ActualCheckInTime      string  `json:"ActualCheckInTime"`
	ShareRefundAmount      float64 `json:"ShareRefundAmount"`
	Quantity               int     `json:"Quantity"`
	MobilePhone            string  `json:"MobilePhone"`
	UserNamePinyin         string  `json:"UserNamePinyin"`
	ActualDepartureTimeUTC string  `json:"ActualDepartureTimeUTC"`
	ActualCheckInTimeUTC   string  `json:"ActualCheckInTimeUTC"`
}

type BedInfo struct {
	BedTypeDesc   string `json:"BedTypeDesc"`
	BedDetailDesc string `json:"BedDetailDesc"`
}

type RoomInfo struct {
	Room                        int         `json:"Room"`
	RoomName                    string      `json:"RoomName"`
	RoomEnName                  string      `json:"RoomEnName"`
	BasicRoomTypeName           string      `json:"BasicRoomTypeName"`
	BasicRoomTypeEnName         string      `json:"BasicRoomTypeEnName"`
	BedType                     string      `json:"BedType"`
	Eta                         string      `json:"ETA"`
	Etd                         string      `json:"ETD"`
	Price                       float64     `json:"Price"`
	Currency                    string      `json:"Currency"`
	Exchange                    float64     `json:"Exchange"`
	Breakfast                   int         `json:"Breakfast"`
	AddBreakfast                int         `json:"AddBreakfast"`
	AddBreakfastPrice           float64     `json:"AddBreakfastPrice"`
	Meals                       int         `json:"Meals"`
	RemainQuantity              int         `json:"RemainQuantity"`
	RefundQuantity              int         `json:"RefundQuantity"`
	BedInfo                     BedInfo     `json:"BedInfo"`
	SettlementRemittedTaxAmount interface{} `json:"SettlementRemittedTaxAmount"`
}

type PaymentItem struct {
	PrePayType    string  `json:"PrePayType"`
	PaymentStatus string  `json:"PaymentStatus"`
	Currency      string  `json:"Currency"`
	Exchange      float64 `json:"Exchange"`
	Amount        float64 `json:"Amount"`
}

type PaymentInfo struct {
	PaymentItemList []PaymentItem `json:"PaymentItemList"`
}

type PaymentReceived struct {
	BillNo          string      `json:"BillNo"`
	RelatedBillNo   interface{} `json:"RelatedBillNo"`
	Amount          float64     `json:"Amount"`
	Currency        string      `json:"Currency"`
	ExchangeRate    float64     `json:"ExchangeRate"`
	TransactionType string      `json:"TransactionType"`
	PaymentStatus   string      `json:"PaymentStatus"`
	PaymentTime     string      `json:"PaymentTime"`
	PaymentType     string      `json:"PaymentType"`
}

type RefundAmountInfo struct {
	TotalChargeOfRoomAmount float64 `json:"TotalChargeOfRoomAmount"`
	RefundTotalAmount       float64 `json:"RefundTotalAmount"`
	SubcriptionFee          float64 `json:"SubcriptionFee"`
}

type AuditDataInfo struct {
	CheckInDate  string      `json:"CheckInDate"`
	CheckOutDate string      `json:"CheckOutDate"`
	ClientName   string      `json:"ClientName"`
	EmployeeID   string      `json:"EmployeeId"`
	RoomNo       interface{} `json:"RoomNo"`
	AuditStatus  string      `json:"AuditStatus"`
}

type HotelOrderInfo struct {
	OrderID                          string             `json:"OrderID"`
	TripID                           string             `json:"TripId"`
	UID                              string             `json:"UID"`
	PreEmployeeID                    string             `json:"PreEmployeeID"`
	PreEmployeeName                  string             `json:"PreEmployeeName"`
	Currency                         string             `json:"Currency"`
	Amount                           float64            `json:"Amount"`
	AmountRMB                        float64            `json:"AmountRMB"`
	CustomPayAmount                  float64            `json:"CustomPayAmount"`
	SettlementAmount                 float64            `json:"SettlementAmount"`
	PostAmount                       float64            `json:"PostAmount"`
	PayType                          string             `json:"PayType"`
	CustomPayCurrency                string             `json:"CustomPayCurrency"`
	CustomPayExchange                float64            `json:"CustomPayExchange"`
	SettlementCurrency               string             `json:"SettlementCurrency"`
	SettlementExchange               float64            `json:"SettlementExchange"`
	IsMixPayment                     string             `json:"IsMixPayment"`
	SettlementACCNTAmt               float64            `json:"SettlementACCNTAmt"`
	SettlementPersonAmt              float64            `json:"SettlementPersonAmt"`
	AddedFees                        float64            `json:"AddedFees"`
	FrontendServiceFee               float64            `json:"FrontendServiceFee"`
	HotelType                        string             `json:"HotelType"`
	HotelName                        string             `json:"SupplierHotelName"`
	HotelEnName                      string             `json:"HotelEnName"`
	StarLicence                      string             `json:"StarLicence"`
	Star                             int                `json:"Star"`
	CustomerEval                     float64            `json:"CustomerEval"`
	Telephone                        string             `json:"Telephone"`
	Address                          string             `json:"Address"`
	StartTime                        string             `json:"StartTime"`
	EndTime                          string             `json:"EndTime"`
	ProvinceEname                    string             `json:"ProvinceEname"`
	CityID                           int                `json:"CityID"`
	CityName                         string             `json:"CityName"`
	CityEnName                       string             `json:"CityEnName"`
	RoomName                         string             `json:"RoomName"`
	IsDomestic                       bool               `json:"IsDomestic"`
	RoomType                         string             `json:"RoomTypeID"`
	MealType                         int                `json:"MealType"`
	MasterHotelID                    int                `json:"MasterHotelID"`
	DistrictID                       int                `json:"DistrictID"`
	DistrictName                     string             `json:"DistrictName"`
	OrderStatus                      string             `json:"OrderStatus"`
	OrderDetailStatus                string             `json:"OrderDetailStatus"`
	OrderDetailStatusName            string             `json:"OrderDetailStatusName"`
	OrderDate                        string             `json:"OrderDate"`
	LastCancelTime                   string             `json:"LastCancelTime"`
	CancelTime                       string             `json:"CancelTime"`
	CancelReasonCode                 string             `json:"CancelReasonCode"`
	CancelReasonDesc                 string             `json:"CancelReasonDesc"`
	FundAccount                      FlexInt            `json:"FundAccount"`
	SubAccount                       FlexInt            `json:"SubAccount"`
	BalanceType                      string             `json:"BalanceType"`
	CorpPayType                      string             `json:"CorpPayType"`
	ContactEmail                     string             `json:"ContactEmail"`
	ContactName                      string             `json:"ContactName"`
	ContactTel                       string             `json:"ContactTel"`
	HotelConfirmNo                   string             `json:"HotelConfirmNo"`
	JourneyNo                        string             `json:"JourneyNo"`
	CostCenter                       string             `json:"CostCenter"`
	CostCenter2                      string             `json:"CostCenter2"`
	CostCenter3                      string             `json:"CostCenter3"`
	CostCenter4                      string             `json:"CostCenter4"`
	CostCenter5                      string             `json:"CostCenter5"`
	CostCenter6                      string             `json:"CostCenter6"`
	JourneyReason                    string             `json:"JourneyReason"`
	Project                          string             `json:"Project"`
	Defineflag                       interface{}        `json:"Defineflag"`
	Defineflag2                      interface{}        `json:"Defineflag2"`
	AuthorizeStatus                  string             `json:"AuthorizeStatus"`
	ConfirmPerson                    string             `json:"ConfirmPerson"`
	ConfirmPerson2                   string             `json:"ConfirmPerson2"`
	ConfirmPersonCC                  interface{}        `json:"ConfirmPersonCC"`
	ConfirmPersonCC2                 interface{}        `json:"ConfirmPersonCC2"`
	ConfirmType                      string             `json:"ConfirmType"`
	ConfirmType2                     string             `json:"ConfirmType2"`
	GLat                             float64            `json:"GLat"`
	GLon                             float64            `json:"GLon"`
	GDLat                            float64            `json:"GDLat"`
	GDLon                            float64            `json:"GDLon"`
	Lat                              float64            `json:"Lat"`
	Lon                              float64            `json:"Lon"`
	LowPriceRC                       interface{}        `json:"LowPriceRC"`
	LowPriceRCInfo                   interface{}        `json:"LowPriceRCInfo"`
	LowPriceRCVV                     interface{}        `json:"LowPriceRCVV"`
	AgreementRC                      interface{}        `json:"AgreementRC"`
	AgreementRCInfo                  interface{}        `json:"AgreementRCInfo"`
	AgreementRCVV                    interface{}        `json:"AgreementRCVV"`
	MinPriceRC                       interface{}        `json:"MinPriceRC"`
	MinPriceRCVV                     interface{}        `json:"MinPriceRCVV"`
	RoomQuantity                     int                `json:"RoomQuantity"`
	RoomDays                         int                `json:"RoomDays"`
	IsHasSpecialInvoice              string             `json:"IsHasSpecialInvoice"`
	IsAutoAmadeus                    string             `json:"IsAutoAmadeus"`
	IsHandAmadeus                    string             `json:"IsHandAmadeus"`
	ServerFrom                       string             `json:"ServerFrom"`
	IsOfficialCard                   interface{}        `json:"IsOfficialCard"`
	CouponAmount                     float64            `json:"CouponAmount"`
	TMCPriceType                     string             `json:"TMCPriceType"`
	PlatformOrderID                  string             `json:"PlatformOrderID"`
	LadderDeductAmount               float64            `json:"LadderDeductAmount"`
	TPMaxPrice                       float64            `json:"TPMaxPrice"`
	ClientName                       string             `json:"ClientName"`
	ClientInfo                       []ClientInfo       `json:"ClientInfo"`
	RoomInfo                         []RoomInfo         `json:"RoomInfo"`
	AuditInfo                        interface{}        `json:"AuditInfo"`
	RefundInfo                       []RefundInfoEntity `json:"RefundInfo"`
	PaymentInfo                      PaymentInfo        `json:"PaymentInfo"`
	PaymentReceivedList              []PaymentReceived  `json:"PaymentReceivedList"`
	StepPaymentList                  interface{}        `json:"StepPaymentList "`
	IsOnline                         string             `json:"IsOnline"`
	CorporationID                    string             `json:"CorporationId"`
	CountryName                      string             `json:"CountryName"`
	ProvinceID                       int                `json:"ProvinceId"`
	ProvinceName                     string             `json:"ProvinceName"`
	CountryCode                      string             `json:"CountryCode"`
	GuaranteeAmountType              int                `json:"GuaranteeAmountType"`
	GuaranteeAmount                  float64            `json:"GuaranteeAmount"`
	GuaranteeAmountCurrency          string             `json:"GuaranteeAmountCurrency"`
	PaidGuaranteeAmount              float64            `json:"PaidGuaranteeAmount"`
	PaidGuaranteeAmountCurrency      interface{}        `json:"PaidGuaranteeAmountCurrency"`
	PolicyPreApprovalNo              interface{}        `json:"PolicyPreApprovalNo"`
	ContractRoomExistedFlag          string             `json:"ContractRoomExistedFlag"`
	ApplyFormDetailList              json.RawMessage    `json:"ApplyFormDetailList"`
	CancelFormDetailList             json.RawMessage    `json:"CancelFormDetailList"`
	DockingVendorPlatform            interface{}        `json:"DockingVendorPlatform"`
	DockingVendorPlatformAccount     interface{}        `json:"DockingVendorPlatformAccount"`
	CorpDockingInfoList              interface{}        `json:"CorpDockingInfoList"`
	HotelTypeCode                    string             `json:"HotelTypeCode"`
	BrandName                        string             `json:"BrandName"`
	UsedVccPay                       bool               `json:"UsedVccPay"`
	RefundAmountInfo                 RefundAmountInfo   `json:"RefundAmountInfo"`
	TravelControlMode                string             `json:"TravelControlMode"`
	HtlStandardCtlRule               string             `json:"HtlStandardCtlRule"`
	HtlStandardSharePercentage       string             `json:"HtlStandardSharePercentage"`
	HtlStandardFixPrice              interface{}        `json:"HtlStandardFixPrice"`
	RepeatBookingOrderList           interface{}        `json:"RepeatBookingOrderList"`
	RepeatBookingRC                  interface{}        `json:"RepeatBookingRC"`
	RepeatBookingDesc                interface{}        `json:"RepeatBookingDesc"`
	RepeatBookingRCVV                interface{}        `json:"RepeatBookingRCVV"`
	Scene                            interface{}        `json:"Scene"`
	MobilePhone                      string             `json:"MobilePhone"`
	UserNamePinyin                   string             `json:"UserNamePinyin"`
	AuditDataInfoList                []AuditDataInfo    `json:"AuditDataInfoList"`
	ConfigCurrency                   string             `json:"ConfigCurrency"`
	ConfigExchangeToSettlement       float64            `json:"ConfigExchangeToSettlement"`
	TpConfigMinPrice                 float64            `json:"TpConfigMinPrice"`
	TpConfigMaxPrice                 float64            `json:"TpConfigMaxPrice"`
	SettlementRemittedTaxTotalAmount interface{}        `json:"SettlementRemittedTaxTotalAmount"`
	ArrivalDayUTC                    string             `json:"ArrivalDayUTC"`
	DepartureDayUTC                  string             `json:"DepartureDayUTC"`
	// for partial room cancel
	//ActualRoomNightInfoList          []RoomNightInfoType `json:"actualRoomNightInfoList"`
}

type RoomNightInfoType struct {
	Date           string                    `json:"date"` // (yyyy-MM-dd)
	Quantity       int64                     `json:"quantity"`
	ClientInfoList []RoomNightClientInfoType `json:"clientInfoList"`
	DateUTC        string                    `json:"dateUTC"` // (yyyy-MM-dd'T'HH:mm:ssXX)
}

type RoomNightClientInfoType struct {
	ClientInfoId int64  `json:"clientInfoId"`
	RoomIndex    int64  `json:"roomIndex"`
	Name         string `json:"name"`
}

type Itinerary struct {
	JourneyNO                    string           `json:"JourneyNO"`
	FlightOrderInfoList          []FlightOrder    `json:"FlightOrderInfoList"`
	HotelOrderInfoList           []HotelOrderInfo `json:"HotelOrderInfoList"`
	TrainOrderInfoList           interface{}      `json:"TrainOrderInfoList"`
	HotelSupplementOrderInfoList interface{}      `json:"HotelSupplementOrderInfoList"`
	TrainSupplementOrderInfoList interface{}      `json:"TrainSupplementOrderInfoList"`
	CarOrderInfoList             interface{}      `json:"CarOrderInfoList"`
	CarQuickOrderInfoList        interface{}      `json:"CarQuickOrderInfoList"`
	DomPickUpOrderInfoList       interface{}      `json:"DomPickUpOrderInfoList"`
	DomCharterCarOrderInfoList   interface{}      `json:"DomCharterCarOrderInfoList"`
	IntlTrainOrderInfoList       interface{}      `json:"IntlTrainOrderInfoList"`
}

type RefundPayment struct {
	RefundID      interface{} `json:"RefundID"`
	RefundChannel string      `json:"RefundChannel"`
	Amount        float64     `json:"Amount"`
}

type RefundDetail struct {
	RoomAmount float64 `json:"RoomAmount"`
}

type RefundInfoEntity struct {
	RefundInfoID         FlexInt         `json:"RefundInfoID"`
	RefundCustomerAmount float64         `json:"RefundCustomerAmount"`
	Currency             string          `json:"Currency"`
	ExchangeRate         float64         `json:"ExchangeRate"`
	RefundPaymentList    []RefundPayment `json:"RefundPaymentList"`
	RefundDetailList     []RefundDetail  `json:"RefundDetailList"`
	RoomIndex            int64           `json:"roomIndex"`
}

type OrderAirport struct {
	Name        string `json:"Name"`
	NameEn      string `json:"NameEn"`
	Shortname   string `json:"Shortname"`
	ShortnameEn string `json:"ShortnameEn"`
}

type OrderFlightInfo struct {
	Sequence                    string       `json:"Sequence"`
	Flight                      string       `json:"Flight"`
	AirLineCode                 string       `json:"AirLineCode"`
	AirLineName                 string       `json:"AirLineName"`
	Remark                      string       `json:"Remark"`
	TakeoffTime                 string       `json:"TakeoffTime"`
	ArrivalTime                 string       `json:"ArrivalTime"`
	DCityName                   string       `json:"DCityName"`
	DCityNameEN                 string       `json:"DCityNameEN"`
	DCityCode                   string       `json:"DCityCode"`
	DPortName                   string       `json:"DPortName"`
	DPortCode                   string       `json:"DPortCode"`
	Agreement                   string       `json:"Agreement"`
	Amount                      float64      `json:"Amount"`
	Price                       float64      `json:"Price"`
	PrintPrice                  float64      `json:"PrintPrice"`
	PriceRate                   interface{}  `json:"PriceRate"`
	StandardPrice               interface{}  `json:"StandardPrice"`
	HasMeal                     interface{}  `json:"HasMeal"`
	OilFee                      float64      `json:"OilFee"`
	Tax                         float64      `json:"Tax"`
	Bindtype                    string       `json:"Bindtype"`
	BindNum                     int          `json:"BindNum"`
	BindAmount                  float64      `json:"BindAmount"`
	ServerFee                   float64      `json:"ServerFee"`
	Subsidy                     float64      `json:"Subsidy"`
	AgeType                     string       `json:"AgeType"`
	Class                       string       `json:"Class"`
	ClassName                   string       `json:"ClassName"`
	ClassNameNew                string       `json:"ClassNameNew"`
	SubClass                    string       `json:"SubClass"`
	OfficeNo                    string       `json:"OfficeNo"`
	NonRer                      string       `json:"NonRer"`
	RerNotes                    string       `json:"RerNotes"`
	NonRef                      string       `json:"NonRef"`
	RefNotes                    string       `json:"RefNotes"`
	NonEnd                      string       `json:"NonEnd"`
	EndNotes                    string       `json:"EndNotes"`
	Adtk                        string       `json:"Adtk"`
	FuelMileage                 int          `json:"FuelMileage"`
	EClassStandardPrice         float64      `json:"EClassStandardPrice"`
	SpeicalClassTypeName        string       `json:"SpeicalClassTypeName"`
	SpeicalClassTypeDescription string       `json:"SpeicalClassTypeDescription"`
	CraftType                   string       `json:"CraftType"`
	DAirport                    OrderAirport `json:"DAirport"`
	ACityName                   string       `json:"ACityName"`
	ACityNameEN                 string       `json:"ACityNameEN"`
	ACityCode                   string       `json:"ACityCode"`
	APortName                   string       `json:"APortName"`
	APortCode                   string       `json:"APortCode"`
	AAirport                    OrderAirport `json:"AAirport"`
	IsOpenTran                  string       `json:"IsOpenTran"`
	IsSurface                   string       `json:"IsSurface"`
	Reason                      string       `json:"Reason"`
	ReasonDesc                  string       `json:"ReasonDesc"`
	PreBookReason               string       `json:"PreBookReason"`
	PreBookReasonDesc           string       `json:"PreBookReasonDesc"`
	LowFlight                   string       `json:"LowFlight"`
	LowClass                    string       `json:"LowClass"`
	LowestPrice                 float64      `json:"LowestPrice"`
	LowRate                     float64      `json:"LowRate"`
	LowDTime                    string       `json:"LowDTime"`
	Tpm                         int          `json:"Tpm"`
	ClassReason                 string       `json:"ClassReason"`
	ClassReasonDesc             string       `json:"ClassReasonDesc"`
	AgreementReason             string       `json:"AgreementReason"`
	AgreementReasonDesc         string       `json:"AgreementReasonDesc"`
	DistanceReason              string       `json:"DistanceReason"`
	DistanceReasonDesc          string       `json:"DistanceReasonDesc"`
	FlightTime                  int          `json:"FlightTime"`
	AirlineRecordNo             string       `json:"AirlineRecordNo"`
	FlightStopInfoList          interface{}  `json:"FlightStopInfoList"`
	SaleType                    string       `json:"SaleType"`
	PNR                         string       `json:"PNR"`
	SegmentNo                   int          `json:"SegmentNo"`
	ItineraryFee                float64      `json:"ItineraryFee"`
	ItineraryPassengers         interface{}  `json:"ItineraryPassengers"`
	AgreementCode               string       `json:"AgreementCode"`
	DepartureCountryCode        string       `json:"DepartureCountryCode"`
	ArrivalCountryCode          string       `json:"ArrivalCountryCode"`
	TakeOffTimeUTC              string       `json:"TakeOffTimeUTC"`
	ArrivalTimeUTC              string       `json:"ArrivalTimeUTC"`
	Alliance                    string       `json:"Alliance"`
	SectorType                  interface{}  `json:"SectorType"`
	DepartureDistrictCode       string       `json:"DepartureDistrictCode"`
	ArrivalDistrictCode         string       `json:"ArrivalDistrictCode"`
	SettlementAccntAmount       float64      `json:"SettlementAccntAmount"`
	SettlementAccntPrice        float64      `json:"SettlementAccntPrice"`
	SettlementAccntOilFee       float64      `json:"SettlementAccntOilFee"`
	SettlementAccntTax          float64      `json:"SettlementAccntTax"`
	SettlementIndividualAmount  float64      `json:"SettlementIndividualAmount"`
	SettlementIndividualPrice   float64      `json:"SettlementIndividualPrice"`
	SettlementIndividualOilFee  float64      `json:"SettlementIndividualOilFee"`
	SettlementIndividualTax     float64      `json:"SettlementIndividualTax"`
	ShareFlightNo               string       `json:"ShareFlightNo"`
	ReasonCodeInfoList          interface{}  `json:"ReasonCodeInfoList"`
	RoundTripType               interface{}  `json:"RoundTripType"`
	HighestPriceReason          interface{}  `json:"HighestPriceReason"`
	HighestPriceReasonDesc      interface{}  `json:"HighestPriceReasonDesc"`
	TimeReason                  interface{}  `json:"TimeReason"`
	TimeReasonDesc              interface{}  `json:"TimeReasonDesc"`
	LowArrivalTime              string       `json:"LowArrivalTime"`
	DiscountReasonCode          interface{}  `json:"DiscountReasonCode"`
	DiscountReasonDesc          interface{}  `json:"DiscountReasonDesc"`
	LowDPort                    interface{}  `json:"LowDPort"`
	LowAPort                    interface{}  `json:"LowAPort"`
	ClassControlExtend          interface{}  `json:"ClassControlExtend"`
	ClassControlResult          interface{}  `json:"ClassControlResult"`
	LowControlResult            interface{}  `json:"LowControlResult"`
	TimeControlResult           interface{}  `json:"TimeControlResult"`
	PreBookControlResult        interface{}  `json:"PreBookControlResult"`
}

type FlightBasicInfo struct {
	OrderID           string `json:"OrderID"`
	TripID            string `json:"TripId"`
	OrderStatus       string `json:"OrderStatus"`
	OrderStatusCode   string `json:"OrderStatusCode"`
	UID               string `json:"UID"`
	PreEmployName     string `json:"PreEmployName"`
	EmployeeID        string `json:"EmployeeID"`
	AccountID         int    `json:"AccountID"`
	SubAccountID      int    `json:"SubAccountID"`
	CorpPayType       string `json:"CorpPayType"`
	CreateTime        string `json:"CreateTime"`
	FinishDate        string `json:"FinishDate"`
	PrintTicketTime   string `json:"PrintTicketTime"`
	FlightClass       string `json:"FlightClass"`
	FlightWay         string `json:"FlightWay"`
	Remarks           string `json:"Remarks"`
	PreBookDays       int    `json:"PreBookDays"`
	ServiceDetailInfo struct {
		BaseServiceFee             float64 `json:"BaseServiceFee"`
		BindServiceFee             float64 `json:"BindServiceFee"`
		SpecialServiceFee          float64 `json:"SpecialServiceFee"`
		UnWorkTimeServiceFee       float64 `json:"UnWorkTimeServiceFee"`
		VIPServiceFee              float64 `json:"VIPServiceFee"`
		ItineraryFeeForRMB         float64 `json:"ItineraryFeeForRMB"`
		ItineraryFeeForForeign     float64 `json:"ItineraryFeeForForeign"`
		TechnicalServiceFee        float64 `json:"TechnicalServiceFee"`
		PresentInsuranceServiceFee float64 `json:"PresentInsuranceServiceFee"`
	} `json:"ServiceDetailInfo"`
	NBillingType     string      `json:"NBillingType"`
	TicketStatus     string      `json:"TicketStatus"`
	RebookOrderID    string      `json:"RebookOrderID"`
	ServerFrom       string      `json:"ServerFrom"`
	IsOfficialCard   string      `json:"IsOfficialCard"`
	BookingChannel   string      `json:"BookingChannel"`
	PlatformOrderID  string      `json:"PlatformOrderID"`
	PayExchangeRate  float64     `json:"PayExchangeRate"`
	OperationCode    int         `json:"OperationCode"`
	Amount           float64     `json:"Amount"`
	TravelMoney      float64     `json:"TravelMoney"`
	ChangeAmount     float64     `json:"ChangeAmount"`
	RefundAmount     float64     `json:"RefundAmount"`
	CCardPayFee      float64     `json:"CCardPayFee"`
	SendTicketFee    float64     `json:"SendTicketFee"`
	InsuranceFee     float64     `json:"InsuranceFee"`
	PrepayType       string      `json:"PrepayType"`
	TotalServiceFee  float64     `json:"TotalServiceFee"`
	Currency         string      `json:"Currency"`
	ForeignAmount    float64     `json:"ForeignAmount"`
	Refundable       bool        `json:"Refundable"`
	Rebookable       bool        `json:"Rebookable"`
	JourneyID        string      `json:"JourneyID"`
	CostCenter       string      `json:"CostCenter"`
	CostCenter2      string      `json:"CostCenter2"`
	CostCenter3      string      `json:"CostCenter3"`
	CostCenter4      string      `json:"CostCenter4"`
	CostCenter5      string      `json:"CostCenter5"`
	CostCenter6      string      `json:"CostCenter6"`
	DefineFlag       string      `json:"DefineFlag"`
	DefineFlag2      string      `json:"DefineFlag2"`
	JourneyReason    string      `json:"JourneyReason"`
	Project          string      `json:"Project"`
	AuditStatus      string      `json:"AuditStatus"`
	ConfirmPerson    string      `json:"ConfirmPerson"`
	ConfirmPerson2   string      `json:"ConfirmPerson2"`
	ConfirmPersonCC  interface{} `json:"ConfirmPersonCC"`
	ConfirmPersonCC2 interface{} `json:"ConfirmPersonCC2"`
	ConfirmType      string      `json:"ConfirmType"`
	ConfirmType2     string      `json:"ConfirmType2"`
	PayMixed         bool        `json:"PayMixed"`
	PaymentItemList  []struct {
		PayType        string  `json:"PayType"`
		PayAmount      float64 `json:"PayAmount"`
		ItemDetailList []struct {
			Sequence      int     `json:"Sequence"`
			PassengerId   int64   `json:"PassengerId"`
			PassengerName string  `json:"PassengerName"`
			FeeCode       string  `json:"FeeCode"`
			FeeAmount     float64 `json:"FeeAmount"`
		} `json:"ItemDetailList"`
	} `json:"PaymentItemList"`
	IsOnline                     string      `json:"IsOnline"`
	CorporationId                string      `json:"CorporationId"`
	DockingVendorPlatform        interface{} `json:"DockingVendorPlatform"`
	DockingVendorPlatformAccount interface{} `json:"DockingVendorPlatformAccount"`
	CorpDockingInfoList          interface{} `json:"CorpDockingInfoList"`
	AirlineCountryIdEqualAccount interface{} `json:"airlineCountryIdEqualAccount"`
	PreEmployNamePinYin          string      `json:"PreEmployNamePinYin"`
	CancelAble                   bool        `json:"CancelAble"`
	NewTotalServiceFee           float64     `json:"NewTotalServiceFee"`
}

type RelatedOrderEntity struct {
	RelatedOrderID int64 `json:"RelatedOrderID"`
}

type FlightOrder struct {
	BasicInfo    FlightBasicInfo `json:"BasicInfo"`
	DeliveryInfo struct {
		DeliveryInfo    string `json:"DeliveryInfo"`
		ContactPhone    string `json:"ContactPhone"`
		ContactMobile   string `json:"ContactMobile"`
		ContactName     string `json:"ContactName"`
		DeliveryAddress string `json:"DeliveryAddress"`
		ContactEmail    string `json:"ContactEmail"`
		ProvideBillType string `json:"ProvideBillType"`
	} `json:"DeliveryInfo"`
	FlightInfo            []*OrderFlightInfo   `json:"FlightInfo"`
	PassengerInfo         []PassengerInfo      `json:"PassengerInfo"`
	RefundInfo            []FlightRefundInfo   `json:"RefundInfo"`
	FlightChangeInfo      interface{}          `json:"FlightChangeInfo"`
	RelatedOrderList      []RelatedOrderEntity `json:"RelatedOrderList"`
	FlightTicketPrintInfo struct {
		SegmentPrintInfoList   interface{} `json:"SegmentPrintInfoList"`
		RegularExpressInfoList interface{} `json:"RegularExpressInfoList"`
	} `json:"FlightTicketPrintInfo"`
	PackageList        interface{} `json:"PackageList"`
	XProductDetailList interface{} `json:"XProductDetailList"`
	// FlightOrderFeeDetailList interface{} `json:"FlightOrderFeeDetailList"`
	TripRecordInfoList []struct {
		TripId                int     `json:"TripId"`
		Sequence              int     `json:"Sequence"`
		PassengerName         string  `json:"PassengerName"`
		RecordStatus          string  `json:"RecordStatus"`
		ValidFlag             bool    `json:"ValidFlag"`
		FlightClass           string  `json:"FlightClass"`
		Flight                string  `json:"Flight"`
		ClassGrade            string  `json:"ClassGrade"`
		SubClass              string  `json:"SubClass"`
		TakeOffTime           string  `json:"TakeOffTime"`
		ArrivalTime           string  `json:"ArrivalTime"`
		DCity                 int     `json:"DCity"`
		DPort                 string  `json:"DPort"`
		DPortBuilding         string  `json:"DPortBuilding"`
		DPortBuildingId       int     `json:"DPortBuildingId"`
		ACity                 int     `json:"ACity"`
		APort                 string  `json:"APort"`
		APortBuilding         string  `json:"APortBuilding"`
		APortBuildingId       int     `json:"APortBuildingId"`
		OpenTranFlag          bool    `json:"OpenTranFlag"`
		PrintPrice            float64 `json:"PrintPrice"`
		Oil                   float64 `json:"Oil"`
		Tax                   float64 `json:"Tax"`
		RecordNo              string  `json:"RecordNo"`
		AirlineRecordNo       string  `json:"AirlineRecordNo"`
		SharedFlag            bool    `json:"SharedFlag"`
		SharedFlight          string  `json:"SharedFlight"`
		SurfaceFlag           bool    `json:"SurfaceFlag"`
		AirLineCode           string  `json:"AirLineCode"`
		TicketNo              string  `json:"TicketNo"`
		TicketNoStatus        int     `json:"TicketNoStatus"`
		DepartureCityName     string  `json:"DepartureCityName"`
		DepartureAirPortName  string  `json:"DepartureAirPortName"`
		ArrivalCityName       string  `json:"ArrivalCityName"`
		ArrivalAirportName    string  `json:"ArrivalAirportName"`
		DepartureDistrictCode string  `json:"DepartureDistrictCode"`
		ArrivalDistrictCode   string  `json:"ArrivalDistrictCode"`
		ClassTypeName         string  `json:"ClassTypeName"`
		AirLineName           string  `json:"AirLineName"`
	} `json:"TripRecordInfoList"`
	ExtraBaggageInfos *ExtraBaggageInfo `json:"XProductInfo"`
}

type ExtraBaggageInfo struct {
	ProductDetailList []*BaggageProductDetail `json:"ProductDetailList"`
}

type BaggageProductDetail struct {
	ProductOrderId              int64                      `json:"ProductOrderId"`
	PassengerFlightRelationList []*PassengerFlightRelation `json:"PassengerFlightRelationList"`
}

type PassengerFlightRelation struct {
	PassengerId   int64  `json:"PassengerId"`
	PassengerName string `json:"PassengerName"`
	FlightType    int    `json:"FlightType"`
	FlightNo      string `json:"FlightNo"`
}

// IsChinaDomestic I is international, N is domestic
func (f *FlightBasicInfo) IsChinaDomestic() bool {
	return f.FlightClass == "N"
}

type FlightRefundDetail struct {
	Sequence                   int       `json:"Sequence"`
	AirLineCode                string    `json:"AirLineCode"`
	TicketNo                   string    `json:"TicketNo"`
	TicketNoSignCode           string    `json:"TicketNoSignCode"`
	Flight                     string    `json:"Flight"`
	PassengerName              string    `json:"PassengerName"`
	RefundFee                  float64   `json:"RefundFee"`
	RefundRate                 float64   `json:"RefundRate"`
	RefundServiceFee           float64   `json:"RefundServiceFee"`
	Subsidy                    float64   `json:"Subsidy"`
	RebookingListID            int       `json:"RebookingListID"`
	RebookingID                int       `json:"RebookingID"`
	UsedAmount                 float64   `json:"UsedAmount"`
	UsedTax                    float64   `json:"UsedTax"`
	UnusedRefundServiceFeeType int       `json:"UnusedRefundServiceFeeType"`
	UnusedRefundServiceFee     float64   `json:"UnusedRefundServiceFee"`
	RefundItineraryFee         float64   `json:"RefundItineraryFee"`
	RefundInsuranceDetail      *struct{} `json:"RefundInsuranceDetail"`
	TotalEmdAmount             float64   `json:"TotalEmdAmount"`
	ShareFlightNo              *string   `json:"ShareFlightNo"`
	SupplierServiceFee         float64   `json:"SupplierServiceFee"`
}

type RefundProcess struct {
	ProcessName   string `json:"ProcessName"`
	ProcessStatus int    `json:"ProcessStatus"`
}

type FlightRefundInfo struct {
	TokenNO                string               `json:"TokenNO"`
	Audited                string               `json:"Audited"`
	PayCustomerAmount      float64              `json:"PayCustomerAmount"`
	PayCustomerTravelMoney float64              `json:"PayCustomerTravelMoney"`
	RefundApplyTime        string               `json:"RefundAplyTime"`
	RefundAuditedTime      string               `json:"RefundAuditedTime"`
	RefundTime             string               `json:"RefundTime"`
	RefundDesc             string               `json:"RefundDesc"`
	RefundOrderID          int                  `json:"RefundOrderID"`
	RefundStatus           string               `json:"RefundStatus"`
	RefundStatusDesc       string               `json:"RefundStatusDesc"`
	RefundReasonDesc       string               `json:"RefundResonDesc"`
	PrepareApprovalNo      interface{}          `json:"PrepareApprovalNo"`
	RefundEmergency        interface{}          `json:"RefundEmergency"`
	RefundDetails          []FlightRefundDetail `json:"RefundDetail"`
	RefundProcessList      []RefundProcess      `json:"RefundProcessList"`
}

type PassengerInfo struct {
	PassengerBasic PassengerBasic `json:"PassengerBasic"`
	SequenceInfo   []struct {
		Sequence   int `json:"Sequence"`
		TicketInfo []struct {
			AirLineCode      string `json:"AirLineCode"`
			TicketNo         string `json:"TicketNo"`
			TicketNoSignCode string `json:"TicketNoSignCode"`
			Status           string `json:"Status"`
			StatusDesc       string `json:"StatusDesc"`
		} `json:"TicketInfo"`
		InsuranceInfo interface{} `json:"InsuranceInfo"`
		ChangeInfo    []ChangInfo `json:"ChangeInfo"`
	} `json:"SequenceInfo"`
}

type PassengerBasic struct {
	CorpEid         string `json:"CorpEid"`
	PassengerName   string `json:"PassengerName"`
	PassengerNamePY string `json:"PassengerNamePY"`
	NationalityCode string `json:"NationalityCode"`
	NationalityName string `json:"NationalityName"`
	CardTypeName    string `json:"CardTypeName"`
	CardTypeNumber  string `json:"CardTypeNumber"`
	Gender          string `json:"Gender"`
	Birthday        string `json:"Birthday"`
	CostCenter      string `json:"CostCenter"`
	CostCenter2     string `json:"CostCenter2"`
	CostCenter3     string `json:"CostCenter3"`
	CostCenter4     string `json:"CostCenter4"`
	CostCenter5     string `json:"CostCenter5"`
	CostCenter6     string `json:"CostCenter6"`
	Dept1           string `json:"Dept1"`
	Dept2           string `json:"Dept2"`
	Dept3           string `json:"Dept3"`
	Dept4           string `json:"Dept4"`
	Dept5           string `json:"Dept5"`
	Dept6           string `json:"Dept6"`
	Dept7           string `json:"Dept7"`
	Dept8           string `json:"Dept8"`
	Dept9           string `json:"Dept9"`
	Dept10          string `json:"Dept10"`
	CardValid       string `json:"CardValid"`
	NonEmployee     bool   `json:"NonEmployee"`
}

type ChangInfo struct {
	Sequence           int     `json:"Sequence"`
	RebookId           int64   `json:"RebookId"`
	CPrepayType        string  `json:"CPrepayType"`
	PassengerName      string  `json:"PassengerName"`
	CStatus            string  `json:"CStatus"`
	CFee               float64 `json:"CFee"`
	RebookServiceFee   float64 `json:"RebookServiceFee"`
	RebookingTime      string  `json:"RebookingTime"`
	RebookedTime       string  `json:"RebookedTime"`
	OriTicketNO        string  `json:"OriTicketNO"`
	PreTicketNO        string  `json:"PreTicketNO"`
	CTicketNO          string  `json:"CTicketNO"`
	CTicketNoSignCode  string  `json:"CTicketNoSignCode"`
	CAirline           string  `json:"CAirline"`
	CAirlineName       string  `json:"CAirlineName"`
	CAirType           string  `json:"CAirType"`
	CFlight            string  `json:"CFlight"`
	CPrintPrice        float64 `json:"CPrintPrice"`
	OilFee             float64 `json:"OilFee"`
	Tax                float64 `json:"Tax"`
	Subsidy            float64 `json:"Subsidy"`
	SubClass           string  `json:"SubClass"`
	CClass             string  `json:"CClass"`
	CClassName         string  `json:"CClassName"`
	CTakeOffTime       string  `json:"CTakeOffTime"`
	CArrivalTime       string  `json:"CArrivalTime"`
	CDCityName         string  `json:"CDCityName"`
	CDPortName         string  `json:"CDPortName"`
	CDTerminal         string  `json:"CDTerminal"`
	CACityName         string  `json:"CACityName"`
	CAPortName         string  `json:"CAPortName"`
	CATerminal         string  `json:"CATerminal"`
	RebookStatus       string  `json:"RebookStatus"`
	PriceDifferential  float64 `json:"PriceDifferential"`
	DateChangeFee      float64 `json:"DateChangeFee"`
	SendTicketFee      float64 `json:"SendTicketFee"`
	OriAirLineCode     string  `json:"OriAirLineCode"`
	CAirLineCode       string  `json:"CAirLineCode"`
	RebookResonDesc    string  `json:"RebookResonDesc"`
	FlightTime         string  `json:"FlightTime"`
	FlightStopInfoList []struct {
		AirPort      string      `json:"AirPort"`
		StopCity     string      `json:"StopCity"`
		StopCityID   int         `json:"StopCityID"`
		StopTime     string      `json:"StopTime"`
		DistrictCode interface{} `json:"DistrictCode"`
	} `json:"FlightStopInfoList"`
	OilFeeDifferential       float64     `json:"OilFeeDifferential"`
	SpecialClassName         string      `json:"SpecialClassName"`
	SpecialClassDesc         string      `json:"SpecialClassDesc"`
	JounaryNo                string      `json:"JounaryNo"`
	AuthorizeStatus          string      `json:"AuthorizeStatus"`
	CDPortCode               string      `json:"CDPortCode"`
	CAPortCode               string      `json:"CAPortCode"`
	RebookReasonDesc         string      `json:"RebookReasonDesc"`
	RebookType               string      `json:"RebookType"`
	CACityCode               string      `json:"CACityCode"`
	CDCityCode               string      `json:"CDCityCode"`
	TaxDifferential          float64     `json:"TaxDifferential"`
	TakeOffTimeUTC           string      `json:"TakeOffTimeUTC"`
	ArrivalTimeUTC           string      `json:"ArrivalTimeUTC"`
	PriceRate                float64     `json:"PriceRate"`
	DepartureCountryCode     string      `json:"DepartureCountryCode"`
	ArrivalCountryCode       string      `json:"ArrivalCountryCode"`
	FlightWay                string      `json:"FlightWay"`
	DepartureDistrictCode    string      `json:"DepartureDistrictCode"`
	ArrivalDistrictCode      string      `json:"ArrivalDistrictCode"`
	CheckTicketPrice         float64     `json:"CheckTicketPrice"`
	ShareFlightNo            interface{} `json:"ShareFlightNo"`
	OriginalTicketNoSignCode string      `json:"OriginalTicketNoSignCode"`
	PreTicketNoSignCode      string      `json:"PreTicketNoSignCode"`
	RebookReason             string      `json:"RebookReason"`
}

// CancelFormDetailInfoEntity NO API DOCUMENTS YET
type CancelFormDetailInfoEntity struct {
	FormId     interface{}       `json:"FormId"`
	ReasonCode string            `json:"ReasonCode"` // JOURALTER
	ReasonDesc string            `json:"ReasonDesc"`
	CreateTime string            `json:"CreateTime"` // 2024-08-12 16:43:41
	Status     int64             `json:"Status"`     // 3？
	CancelType string            `json:"CancelType"` // "C"?
	ExtendInfo *ExtendInfoEntity `json:"ExtendInfo"`
}

type ApplyFormDetailInfoEntity struct {
	// ReasonCode ITINERARY_CHANGE
	ReasonCode string `json:"ReasonCode"` // 修改原因
	ReasonDesc string `json:"ReasonDesc"` // 原因说明
	// 1:修改酒店
	// 2:修改房型-金额变化
	// 3:提前入住
	// 4:推迟入住
	// 5:中段取消
	// 6:提前离店
	// 7:推迟离店
	// 8:新入离时间和原单完全没有重叠
	// 9:减少间数
	// 10:增加间数
	// 11:修改最晚抵店时间
	// 12:修改入住人姓名
	// 13:修改房型-金额无变化
	// 14:修改联系人
	SceneList []string `json:"SceneList"` // 修改场景
	// 1：已提交
	// 2：待处理
	// 3：与酒店协调中
	// 4：修改成功
	// 5：修改失败
	// 6：修改取消
	// 7：修改取消中
	Status          int64                       `json:"Status"`          // 修改申请单状态
	AppId           int64                       `json:"AppId"`           // 修改申请单号
	ExtendInfo      *ExtendInfoEntity           `json:"ExtendInfo"`      // 单据补充信息
	ModifyRoomNight SimpleModifyRoomNightEntity `json:"ModifyRoomNight"` // 间夜信息
}

type ExtendInfoEntity struct {
	RefundAmount   float64 `json:"RefundAmount"`   // 退款金额（现付订单 输入的金额）
	RefundCurrency string  `json:"RefundCurrency"` // 退款币种（现付订单 输入的币种）
}

type SimpleModifyRoomNightEntity struct {
	// TotleRoomNight ctrip's typo,never mind
	TotleRoomNight    int64                   `json:"TotleRoomNight"` // 总间夜数【不符合公司政策情况下，展示的是减少的间夜（变化量）；其他情况展示剩下的总间夜】
	RoomNightInfoList []SimpleRoomNightEntity // 间夜信息
}
type SimpleRoomNightEntity struct {
	RoomDate string `json:"RoomDate"` // 房间日期
	Quantity int64  `json:"Quantity"` // 房间数量
}
type FlexInt int

func (fi *FlexInt) UnmarshalJSON(b []byte) error {
	if '"' != b[0] {
		return jsonx.Unmarshal(b, (*int)(fi))
	}
	var s string
	if err := jsonx.Unmarshal(b, &s); nil != err {
		return err
	}
	i, err := strconv.Atoi(s)
	if nil != err {
		return err
	}
	*fi = (FlexInt)(i)
	return nil
}
