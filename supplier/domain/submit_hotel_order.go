package domain

import (
	"time"

	common "hotel/common/domain"
)

type Booker struct {
	FirstName string       `json:"firstName"`
	LastName  string       `json:"lastName"`
	Email     string       `json:"email"`
	Phone     common.Phone `json:"phone,omitzero"`
}
type Guest struct {
	FirstName       string `json:"firstName"`       // First name of this guest
	LastName        string `json:"lastName"`        // Last name of this guest
	RoomIndex       int64  `json:"roomIndex"`       // Assigned room index for this guest, starts from 1
	NationalityCode string `json:"nationalityCode"` // Nationality code of this guest
	MobilePhone     string `json:"mobilePhone"`     // prefix would be automatically processed
	Age             int64  `json:"age"`             // Age of this guest, only matters for children
}

type HotelGuest struct {
	Guest
	ActualCheckInTime  time.Time `json:"actualCheckInTime,omitzero"`
	ActualCheckOutTime time.Time `json:"actualCheckOutTime,omitzero"`
}
