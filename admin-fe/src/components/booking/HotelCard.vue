<template>
  <el-card class="hotel-card" shadow="hover" @click="$emit('click', hotel)">
    <div class="hotel-content">
      <!-- Hotel Image -->
      <div class="hotel-image">
        <img
          :src="hotel.images?.[0] || '/placeholder-hotel.svg'"
          :alt="hotel.name"
          @error="handleImageError"
        />
        <div class="image-overlay">
          <el-button size="small" type="primary" @click.stop="viewGallery">
            <el-icon><Picture /></el-icon>
            {{ hotel.images?.length || 0 }} {{ $t('menus.booking.search.images', 'images') }}
          </el-button>
        </div>
      </div>

      <!-- Hotel Info -->
      <div class="hotel-info">
        <div class="hotel-header">
          <h3 class="hotel-name">{{ hotel.name }}</h3>
          <div v-if="hotel.rating && hotel.rating > 0" class="hotel-rating">
            <el-rate
              v-model="hotel.rating"
              disabled
              show-score
              text-color="#ff9900"
              :score-template="$t('menus.booking.search.ratingTemplate', '{value} rating')"
            />
          </div>
        </div>

        <div class="hotel-location">
          <el-icon><Location /></el-icon>
          <span>{{ hotel.address }}</span>
          <span class="city">{{ hotel.city }}, {{ hotel.country }}</span>
        </div>

        <div class="hotel-amenities">
          <el-tag 
            v-for="amenity in displayAmenities" 
            :key="amenity" 
            size="small"
            class="amenity-tag"
          >
            {{ amenity }}
          </el-tag>
          <span v-if="hotel.amenities && hotel.amenities.length > maxAmenities" class="more-amenities">
            +{{ hotel.amenities.length - maxAmenities }} {{ $t('booking.search.more', '更多') }}
          </span>
        </div>

        <p class="hotel-description">{{ truncatedDescription }}</p>
      </div>

      <!-- Pricing Section -->
      <div class="hotel-pricing">
        <div v-if="hotel.minPrice && hotel.minPrice > 0" class="price-section">
          <div class="price-label">{{ $t('booking.hotel.lowestPrice', '最低价格') }}</div>
          <div class="price-display">
            <span class="currency">{{ hotel.currency || 'USD' }}</span>
            <span class="amount">{{ formatPrice(hotel.minPrice) }}</span>
            <span class="period">{{ $t('booking.hotel.perNight', '/ 晚') }}</span>
          </div>
          <div class="price-note">{{ $t('booking.hotel.includingTaxes', '含税费') }}</div>
        </div>

        <div class="action-buttons">
          <el-button
            size="default"
            @click.stop="$emit('view-details', hotel)"
          >
            {{ $t('booking.hotel.viewDetails', '查看详情') }}
          </el-button>
          <el-button
            type="primary"
            size="default"
            @click.stop="$emit('book-now', hotel)"
          >
            {{ $t('booking.hotel.bookNow', '立即预订') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- Room Types Section -->
    <div v-if="hasRooms" class="room-types-section">
      <div class="room-types-header">
        <span class="room-types-title">
          {{ $t('booking.hotel.roomTypes', '房型') }} ({{ hotel.rooms?.length || 0 }})
        </span>
        <el-button
          type="text"
          size="small"
          @click.stop="toggleRoomTypes"
          class="toggle-rooms-btn"
        >
          <span>{{ showRoomTypes ? $t('booking.hotel.hideRooms', '收起') : $t('booking.hotel.showRooms', '展开') }}</span>
          <el-icon class="toggle-icon" :class="{ 'rotated': showRoomTypes }">
            <ArrowDown />
          </el-icon>
        </el-button>
      </div>
      
      <el-collapse-transition>
        <div v-show="showRoomTypes" class="room-types-list">
          <div v-for="room in hotel.rooms" :key="room.roomTypeId" class="room-item">
            <div class="room-header">
              <h4 class="room-name">{{ getI18nText(room.roomName) }}</h4>
              <div v-if="room.rates && room.rates.length > 0" class="room-price">
                <span class="currency">{{ room.rates[0].rate?.currency || 'USD' }}</span>
                <span class="amount">{{ formatPrice(room.rates[0].rate?.amount) }}</span>
                <span class="period">{{ $t('booking.hotel.perNight', '/ 晚') }}</span>
              </div>
            </div>
            
            <div v-if="room.roomDesc" class="room-description">
              {{ getI18nText(room.roomDesc) }}
            </div>
            
            <div class="room-details">
              <div v-if="room.bedDesc" class="room-detail-item">
                <span class="detail-label">{{ $t('booking.hotel.bedType', '床型') }}:</span>
                <span class="detail-value">{{ getI18nText(room.bedDesc) }}</span>
              </div>
              <div v-if="room.occupancy" class="room-detail-item">
                <span class="detail-label">{{ $t('booking.hotel.occupancy', '入住人数') }}:</span>
                <span class="detail-value">{{ room.occupancy }} {{ $t('booking.hotel.persons', '人') }}</span>
              </div>
              <div v-if="room.roomArea" class="room-detail-item">
                <span class="detail-label">{{ $t('booking.hotel.roomArea', '房间面积') }}:</span>
                <span class="detail-value">{{ room.roomArea }}</span>
              </div>
            </div>
            
            <div v-if="room.rates && room.rates.length > 0" class="room-rates">
              <div v-for="rate in room.rates.slice(0, 3)" :key="rate.ratePkgId" class="rate-item">
                <div class="rate-info">
                  <span class="rate-name">{{ rate.ratePlanId || $t('booking.hotel.standardRate', '标准房价') }}</span>
                  <span v-if="rate.available" class="availability available">{{ $t('booking.hotel.available', '可预订') }}</span>
                  <span v-else class="availability unavailable">{{ $t('booking.hotel.unavailable', '不可预订') }}</span>
                </div>
                <div class="rate-price">
                  <span class="currency">{{ rate.rate?.currency || 'USD' }}</span>
                  <span class="amount">{{ formatPrice(rate.rate?.amount) }}</span>
                </div>
              </div>
              <div v-if="room.rates.length > 3" class="more-rates">
                +{{ room.rates.length - 3 }} {{ $t('booking.hotel.moreRates', '更多房价') }}
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- Quick Info Badges -->
    <div class="quick-badges">
      <el-tag v-if="hotel.freeWifi" type="success" size="small">{{ $t('booking.hotel.freeWifi', '免费WiFi') }}</el-tag>
      <el-tag v-if="hotel.freeBreakfast" type="warning" size="small">{{ $t('booking.hotel.freeBreakfast', '免费早餐') }}</el-tag>
      <el-tag v-if="hotel.freeCancellation" type="info" size="small">{{ $t('booking.hotel.freeCancellation', '免费取消') }}</el-tag>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Picture, Location, ArrowDown } from '@element-plus/icons-vue'
import type { HotelDisplay, Hotel } from '@/api/booking/searchApi'
import { useI18nText } from '@/utils/i18n'

interface Props {
  hotel: HotelDisplay | Hotel
  maxAmenities?: number
  maxDescriptionLength?: number
}

interface Emits {
  (e: 'click', hotel: HotelDisplay | Hotel): void
  (e: 'view-details', hotel: HotelDisplay | Hotel): void
  (e: 'book-now', hotel: HotelDisplay | Hotel): void
  (e: 'view-gallery', hotel: HotelDisplay | Hotel): void
}

const props = withDefaults(defineProps<Props>(), {
  maxAmenities: 3,
  maxDescriptionLength: 120
})

const emit = defineEmits<Emits>()
const getI18nText = useI18nText()

// Room types expansion state
const showRoomTypes = ref(false)

// Computed properties
const displayAmenities = computed(() => {
  return props.hotel.amenities?.slice(0, props.maxAmenities) || []
})

const truncatedDescription = computed(() => {
  if (!props.hotel.description) return ''
  
  if (props.hotel.description.length <= props.maxDescriptionLength) {
    return props.hotel.description
  }
  
  return props.hotel.description.substring(0, props.maxDescriptionLength) + '...'
})

// Check if hotel has room data
const hasRooms = computed(() => {
  return props.hotel.rooms && props.hotel.rooms.length > 0
})

// Methods
const formatPrice = (price: number | undefined): string => {
  if (!price) return '0'
  return price.toLocaleString()
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 防止无限循环：如果已经是placeholder图片，就不再重新设置
  if (img.src.includes('placeholder-hotel.svg')) {
    console.warn('Placeholder image failed to load, using inline SVG fallback')
    // 使用内联SVG作为最终fallback
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDI0MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjI0MCIgaGVpZ2h0PSIxODAiIGZpbGw9IiNGNUY3RkEiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg5MCwgNjApIj48cmVjdCB4PSIwIiB5PSIyMCIgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRTVFN0VCIiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMSIvPjxyZWN0IHg9IjgiIHk9IjI4IiB3aWR0aD0iOCIgaGVpZ2h0PSI4IiBmaWxsPSIjRDFENURCIi8+PHJlY3QgeD0iMjIiIHk9IjI4IiB3aWR0aD0iOCIgaGVpZ2h0PSI4IiBmaWxsPSIjRDFENURCIi8+PHJlY3QgeD0iMzYiIHk9IjI4IiB3aWR0aD0iOCIgaGVpZ2h0PSI4IiBmaWxsPSIjRDFENURCIi8+PHJlY3QgeD0iOCIgeT0iNDIiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIGZpbGw9IiNEMUQ1REIiLz48cmVjdCB4PSIyMiIgeT0iNDIiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIGZpbGw9IiNEMUQ1REIiLz48cmVjdCB4PSIzNiIgeT0iNDIiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIGZpbGw9IiNEMUQ1REIiLz48cmVjdCB4PSI0NCIgeT0iNDUiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxNSIgZmlsbD0iIzlDQTNBRiIvPjxwb2x5Z29uIHBvaW50cz0iMCwyMCAzMCw1IDYwLDIwIiBmaWxsPSIjNkI3MjgwIi8+PC9nPjx0ZXh0IHg9IjEyMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUNBM0FGIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTIiPumFkuW6l+WbvueJhzwvdGV4dD48L3N2Zz4='
    return
  }
  // 如果不是placeholder图片失败，则使用placeholder
  img.src = '/placeholder-hotel.svg'
}

const viewGallery = () => {
  emit('view-gallery', props.hotel)
}

const toggleRoomTypes = () => {
  showRoomTypes.value = !showRoomTypes.value
}
</script>

<style scoped lang="scss">
.hotel-card {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .hotel-content {
    display: flex;
    gap: 16px;
    min-height: 200px;
    
    .hotel-image {
      width: 240px;
      height: 180px;
      flex-shrink: 0;
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      .image-overlay {
        position: absolute;
        bottom: 8px;
        right: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover {
        img {
          transform: scale(1.05);
        }
        
        .image-overlay {
          opacity: 1;
        }
      }
    }
    
    .hotel-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .hotel-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .hotel-name {
          font-size: 20px;
          font-weight: 600;
          color: #303133;
          margin: 0;
          line-height: 1.3;
        }
        
        .hotel-rating {
          flex-shrink: 0;
          margin-left: 16px;
        }
      }
      
      .hotel-location {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #606266;
        font-size: 14px;
        
        .city {
          color: #909399;
        }
      }
      
      .hotel-amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        align-items: center;
        
        .amenity-tag {
          background: #f0f9ff;
          border-color: #409EFF;
          color: #409EFF;
        }
        
        .more-amenities {
          color: #409EFF;
          font-size: 12px;
          cursor: pointer;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
      
      .hotel-description {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
        flex: 1;
      }
    }
    
    .hotel-pricing {
      width: 200px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: right;
      
      .price-section {
        .price-label {
          color: #909399;
          font-size: 12px;
          margin-bottom: 4px;
        }
        
        .price-display {
          margin-bottom: 4px;
          
          .currency {
            font-size: 14px;
            color: #606266;
            margin-right: 4px;
          }
          
          .amount {
            font-size: 28px;
            font-weight: 700;
            color: #E6A23C;
            line-height: 1;
          }
          
          .period {
            font-size: 12px;
            color: #909399;
            margin-left: 4px;
          }
        }
        
        .price-note {
          color: #909399;
          font-size: 11px;
        }
      }
      
      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-top: 16px;
      }
    }
  }
  
  .quick-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    z-index: 1;
  }
  
  .room-types-section {
    margin-top: 16px;
    border-top: 1px solid #EBEEF5;
    padding-top: 16px;
    
    .room-types-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .room-types-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .toggle-rooms-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #409EFF;
        
        .toggle-icon {
          transition: transform 0.3s ease;
          
          &.rotated {
            transform: rotate(180deg);
          }
        }
      }
    }
    
    .room-types-list {
      .room-item {
        background: #F8F9FA;
        border: 1px solid #E4E7ED;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .room-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
          
          .room-name {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0;
            flex: 1;
          }
          
          .room-price {
            text-align: right;
            
            .currency {
              font-size: 12px;
              color: #606266;
              margin-right: 2px;
            }
            
            .amount {
              font-size: 18px;
              font-weight: 700;
              color: #E6A23C;
            }
            
            .period {
              font-size: 11px;
              color: #909399;
              margin-left: 2px;
            }
          }
        }
        
        .room-description {
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
          margin-bottom: 12px;
        }
        
        .room-details {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 12px;
          
          .room-detail-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;
            
            .detail-label {
              color: #909399;
              font-weight: 500;
            }
            
            .detail-value {
              color: #606266;
            }
          }
        }
        
        .room-rates {
          .rate-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border: 1px solid #E4E7ED;
            border-radius: 6px;
            margin-bottom: 6px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .rate-info {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .rate-name {
                font-size: 13px;
                color: #303133;
                font-weight: 500;
              }
              
              .availability {
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                
                &.available {
                  background: #F0F9FF;
                  color: #67C23A;
                  border: 1px solid #67C23A;
                }
                
                &.unavailable {
                  background: #FEF0F0;
                  color: #F56C6C;
                  border: 1px solid #F56C6C;
                }
              }
            }
            
            .rate-price {
              .currency {
                font-size: 11px;
                color: #606266;
                margin-right: 2px;
              }
              
              .amount {
                font-size: 14px;
                font-weight: 600;
                color: #E6A23C;
              }
            }
          }
          
          .more-rates {
            text-align: center;
            color: #409EFF;
            font-size: 12px;
            padding: 6px;
            cursor: pointer;
            
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .hotel-card {
    .hotel-content {
      flex-direction: column;
      
      .hotel-image {
        width: 100%;
        height: 200px;
      }
      
      .hotel-pricing {
        width: 100%;
        text-align: left;
        
        .action-buttons {
          flex-direction: row;
        }
      }
    }
  }
}
</style>
