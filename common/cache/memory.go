package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	"hotel/common/log"
)

// CacheItem 缓存项
type CacheItem struct {
	Key       string      // 缓存键
	Value     interface{} // 缓存值
	ExpiresAt time.Time   // 过期时间
	CreatedAt time.Time   // 创建时间
}

// IsExpired 检查是否过期
func (item *CacheItem) IsExpired() bool {
	return !item.ExpiresAt.IsZero() && time.Now().After(item.ExpiresAt)
}

// InMemoryCache 内存缓存实现
type InMemoryCache struct {
	items  map[string]*CacheItem
	mu     sync.RWMutex
	ttl    time.Duration // 默认TTL
	maxSize int          // 最大缓存大小
}

// NewInMemoryCache 创建新的内存缓存
func NewInMemoryCache(ttl time.Duration, maxSize int) *InMemoryCache {
	cache := &InMemoryCache{
		items:   make(map[string]*CacheItem),
		ttl:     ttl,
		maxSize: maxSize,
	}
	
	// 启动清理协程
	go cache.startCleanup()
	
	return cache
}

// Get 获取缓存值
func (c *InMemoryCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	item, exists := c.items[key]
	if !exists {
		return nil, false
	}
	
	if item.IsExpired() {
		// 异步删除过期项
		go c.Delete(key)
		return nil, false
	}
	
	return item.Value, true
}

// Set 设置缓存值
func (c *InMemoryCache) Set(key string, value interface{}, ttl ...time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// 如果超过最大大小，先清理一些旧项
	if len(c.items) >= c.maxSize {
		c.evictOldestUnlocked()
	}
	
	expiration := time.Time{}
	if len(ttl) > 0 && ttl[0] > 0 {
		expiration = time.Now().Add(ttl[0])
	} else if c.ttl > 0 {
		expiration = time.Now().Add(c.ttl)
	}
	
	c.items[key] = &CacheItem{
		Key:       key,
		Value:     value,
		ExpiresAt: expiration,
		CreatedAt: time.Now(),
	}
}

// Delete 删除缓存项
func (c *InMemoryCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.items, key)
}

// Clear 清空所有缓存
func (c *InMemoryCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.items = make(map[string]*CacheItem)
}

// Size 获取缓存大小
func (c *InMemoryCache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return len(c.items)
}

// Keys 获取所有键
func (c *InMemoryCache) Keys() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	keys := make([]string, 0, len(c.items))
	for key := range c.items {
		keys = append(keys, key)
	}
	return keys
}

// evictOldestUnlocked 清除最旧的项（调用时需要持有写锁）
func (c *InMemoryCache) evictOldestUnlocked() {
	if len(c.items) == 0 {
		return
	}
	
	var oldestKey string
	var oldestTime time.Time
	
	for key, item := range c.items {
		if oldestKey == "" || item.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.CreatedAt
		}
	}
	
	if oldestKey != "" {
		delete(c.items, oldestKey)
	}
}

// startCleanup 启动清理协程
func (c *InMemoryCache) startCleanup() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()
	
	for range ticker.C {
		c.cleanupExpired()
	}
}

// cleanupExpired 清理过期项
func (c *InMemoryCache) cleanupExpired() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	expiredKeys := make([]string, 0)
	now := time.Now()
	
	for key, item := range c.items {
		if !item.ExpiresAt.IsZero() && now.After(item.ExpiresAt) {
			expiredKeys = append(expiredKeys, key)
		}
	}
	
	for _, key := range expiredKeys {
		delete(c.items, key)
	}
	
	if len(expiredKeys) > 0 {
		log.Info("Cleaned up %d expired cache items", len(expiredKeys))
	}
}

// GetWithLoader 使用加载器获取缓存值
// 如果缓存不存在，会调用加载器函数获取值并缓存
func (c *InMemoryCache) GetWithLoader(ctx context.Context, key string, loader func(ctx context.Context) (interface{}, error), ttl ...time.Duration) (interface{}, error) {
	// 先尝试从缓存获取
	if value, exists := c.Get(key); exists {
		return value, nil
	}
	
	// 缓存不存在，使用加载器加载
	value, err := loader(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to load cache value for key %s: %w", key, err)
	}
	
	// 缓存加载的值
	c.Set(key, value, ttl...)
	
	return value, nil
}

// Stats 缓存统计信息
type Stats struct {
	Size        int     `json:"size"`
	Hits        int64   `json:"hits"`
	Misses      int64   `json:"misses"`
	HitRatio    float64 `json:"hitRatio"`
	MaxSize     int     `json:"maxSize"`
	DefaultTTL  string  `json:"defaultTTL"`
}

// InMemoryCacheWithStats 带统计的内存缓存
type InMemoryCacheWithStats struct {
	*InMemoryCache
	hits   int64
	misses int64
	mu     sync.RWMutex
}

// NewInMemoryCacheWithStats 创建带统计的内存缓存
func NewInMemoryCacheWithStats(ttl time.Duration, maxSize int) *InMemoryCacheWithStats {
	return &InMemoryCacheWithStats{
		InMemoryCache: NewInMemoryCache(ttl, maxSize),
	}
}

// Get 获取缓存值（带统计）
func (c *InMemoryCacheWithStats) Get(key string) (interface{}, bool) {
	value, exists := c.InMemoryCache.Get(key)
	
	c.mu.Lock()
	if exists {
		c.hits++
	} else {
		c.misses++
	}
	c.mu.Unlock()
	
	return value, exists
}

// GetStats 获取缓存统计
func (c *InMemoryCacheWithStats) GetStats() Stats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	total := c.hits + c.misses
	hitRatio := 0.0
	if total > 0 {
		hitRatio = float64(c.hits) / float64(total)
	}
	
	return Stats{
		Size:       c.Size(),
		Hits:       c.hits,
		Misses:     c.misses,
		HitRatio:   hitRatio,
		MaxSize:    c.maxSize,
		DefaultTTL: c.ttl.String(),
	}
}

// ResetStats 重置统计
func (c *InMemoryCacheWithStats) ResetStats() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.hits = 0
	c.misses = 0
}