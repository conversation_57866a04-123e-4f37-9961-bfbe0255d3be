package retry

import (
	"context"
	"fmt"
	"math"
	"time"

	"hotel/common/log"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries    int           // 最大重试次数
	InitialDelay  time.Duration // 初始延迟
	MaxDelay      time.Duration // 最大延迟
	BackoffFactor float64       // 退避因子
	Jitter        bool          // 是否添加随机抖动
}

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = RetryConfig{
	MaxRetries:    3,
	InitialDelay:  100 * time.Millisecond,
	MaxDelay:      10 * time.Second,
	BackoffFactor: 2.0,
	Jitter:        true,
}

// DatabaseRetryConfig 数据库操作专用重试配置
var DatabaseRetryConfig = RetryConfig{
	MaxRetries:    5,
	InitialDelay:  50 * time.Millisecond,
	MaxDelay:      2 * time.Second,
	BackoffFactor: 1.5,
	Jitter:        true,
}

// RetryFunc 重试函数类型
type RetryFunc func() error

// Result 重试结果
type Result struct {
	Success   bool          // 是否成功
	Attempts  int           // 尝试次数
	TotalTime time.Duration // 总耗时
	LastError error         // 最后一次错误
	AllErrors []error       // 所有错误
}

// WithRetry 带重试机制执行函数
func WithRetry(ctx context.Context, config RetryConfig, retryFunc RetryFunc, isRetryable func(error) bool) *Result {
	start := time.Now()
	result := &Result{
		AllErrors: make([]error, 0, config.MaxRetries+1),
	}

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		result.Attempts = attempt + 1

		// 执行函数
		err := retryFunc()
		if err == nil {
			result.Success = true
			result.TotalTime = time.Since(start)
			log.Infoc(ctx, "Operation succeeded on attempt %d after %v", attempt+1, result.TotalTime)
			return result
		}

		result.LastError = err
		result.AllErrors = append(result.AllErrors, err)

		// 检查是否可重试
		if isRetryable != nil && !isRetryable(err) {
			log.Infoc(ctx, "Error is not retryable: %v", err)
			break
		}

		// 如果这是最后一次尝试，不需要延迟
		if attempt == config.MaxRetries {
			break
		}

		// 计算延迟时间
		delay := calculateDelay(config, attempt)

		log.Infoc(ctx, "Attempt %d failed: %v, retrying in %v", attempt+1, err, delay)

		// 等待延迟时间或上下文取消
		select {
		case <-ctx.Done():
			result.LastError = ctx.Err()
			result.TotalTime = time.Since(start)
			return result
		case <-time.After(delay):
			// 继续下一次尝试
		}
	}

	result.TotalTime = time.Since(start)
	log.Errorc(ctx, "Operation failed after %d attempts in %v, last error: %v",
		result.Attempts, result.TotalTime, result.LastError)
	return result
}

// calculateDelay 计算延迟时间
func calculateDelay(config RetryConfig, attempt int) time.Duration {
	// 指数退避
	delay := time.Duration(float64(config.InitialDelay) * math.Pow(config.BackoffFactor, float64(attempt)))

	// 限制最大延迟
	delay = min(delay, config.MaxDelay)

	// 添加随机抖动以避免惊群效应
	if config.Jitter {
		jitter := time.Duration(float64(delay) * 0.1 * (2*randomFloat() - 1))
		delay += jitter
	}

	return delay
}

// randomFloat 生成 0-1 之间的随机浮点数
func randomFloat() float64 {
	return float64(time.Now().UnixNano()%1000) / 1000.0
}

// IsTransientDatabaseError 判断是否为可重试的数据库错误
func IsTransientDatabaseError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()

	// 常见的可重试数据库错误
	transientErrors := []string{
		"connection refused",
		"connection reset",
		"timeout",
		"deadlock",
		"lock wait timeout",
		"connection lost",
		"server has gone away",
		"temporary failure",
		"too many connections",
		"resource temporarily unavailable",
	}

	for _, pattern := range transientErrors {
		if contains(errMsg, pattern) {
			return true
		}
	}

	return false
}

// contains 检查字符串是否包含子字符串（不区分大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexOf(s, substr) >= 0))
}

// indexOf 查找子字符串位置
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// WithDatabaseRetry 数据库操作专用重试
func WithDatabaseRetry(ctx context.Context, operation RetryFunc) error {
	result := WithRetry(ctx, DatabaseRetryConfig, operation, IsTransientDatabaseError)
	if !result.Success {
		return fmt.Errorf("database operation failed after %d attempts: %w",
			result.Attempts, result.LastError)
	}
	return nil
}

// WithCustomRetry 自定义重试
func WithCustomRetry(ctx context.Context, config RetryConfig, operation RetryFunc) error {
	result := WithRetry(ctx, config, operation, IsTransientDatabaseError)
	if !result.Success {
		return fmt.Errorf("operation failed after %d attempts: %w",
			result.Attempts, result.LastError)
	}
	return nil
}
