package utils

import (
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

func TestCSVRowsToList_Pointer(t *testing.T) {
	mockey.PatchConvey("CSVRowsToList Pointer", t, func() {
		type Row struct {
			Name      *string  `json:"name"`
			ID        *int64   `json:"id"`
			Lat       *float64 `json:"lat"`
			IsOversea *bool    `json:"isOversea"`
			JSON      *string  `json:"json"`
			Int       *int     `json:"int"`
			Uint      *uint    `json:"uint"`
			Int8      *int8    `json:"int8"`
			Uint8     *uint8   `json:"uint8"`
			Int16     *int16   `json:"int16"`
			Uint16    *uint16  `json:"uint16"`
			Int32     *int32   `json:"int32"`
			Uint32    *uint32  `json:"uint32"`
			Int64     *int64   `json:"int64"`
			Uint64    *uint64  `json:"uint64"`
			Float32   *float32 `json:"float32"`
			Float64   *float64 `json:"float64"`
			Bool      *bool    `json:"bool"`
		}
		out := CSVRowsToList([][]string{
			{"name", "id", "lat", "is_oversea", "json", "int", "uint", "int8", "uint8", "int16", "uint16", "int32", "uint32", "int64", "uint64", "float32", "float64", "bool"},
			{"name", "1", "1.1", "false", "NULL", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
			{"name", "1", "1.1", "false", "{}", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"},
		}, new(Row))
		convey.So(*out[0].(*Row).Name, convey.ShouldEqual, "name")
		convey.So(*out[0].(*Row).ID, convey.ShouldEqual, int64(1))
		convey.So(*out[0].(*Row).Lat, convey.ShouldEqual, 1.1)
		convey.So(*out[0].(*Row).IsOversea, convey.ShouldEqual, false)
		convey.So(out[0].(*Row).JSON, convey.ShouldBeNil)
		convey.So(*out[1].(*Row).JSON, convey.ShouldEqual, "{}")
	})
}

func TestCSVRowsToList(t *testing.T) {
	type args struct {
		data      [][]string
		structure interface{}
	}
	type rowStruct struct {
		Name         string    `json:"name"`
		Age          int       `json:"age"`
		Age2         int64     `json:"age2"`
		Score        float64   `json:"score"`
		Birth        time.Time `json:"birth"`
		StringSlice  []string  `json:"stringSlice"`
		Int64Slice   []int64   `json:"int64Slice"`
		IntSlice     []int     `json:"intSlice"`
		Int32Slice   []int32   `json:"int32Slice"`
		Float64Slice []float64 `json:"float64Slice"`
	}
	tests := []struct {
		name    string
		args    args
		wantOut []interface{}
	}{
		{
			name: "normal",
			args: args{
				data: [][]string{
					{"num", "name", "age", "age2", "score", "birth", "string_slice", "int64_slice", "int32_slice", "int_slice", "float64_slice"},
					{"1", "nam1", "8", "9", "0.8", "2024-02-02", `["1","2"]`, `[1,2]`, `[1,2]`, `[1,2]`, `[1,2]`, `[1,2]`},
				},
				structure: new(rowStruct),
			},
			wantOut: []interface{}{
				&rowStruct{
					Name:         "nam1",
					Age:          8,
					Age2:         9,
					Score:        0.8,
					Birth:        time.Time{}, // not supported yet
					StringSlice:  []string{"1", "2"},
					Int64Slice:   []int64{1, 2},
					Int32Slice:   []int32{1, 2},
					IntSlice:     []int{1, 2},
					Float64Slice: []float64{1, 2},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.wantOut, CSVRowsToList(tt.args.data, tt.args.structure), "CSVRowsToList(%v, %v)", tt.args.data, tt.args.structure)
		})
	}
}
