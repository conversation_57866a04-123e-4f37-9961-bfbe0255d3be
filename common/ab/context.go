package ab

import (
	"context"
)

type Meta struct {
	ExpName  string `yaml:"exp_name" json:"expName"`
	ExpGroup int64  `yaml:"exp_group" json:"expGroup"`
}

type ctxKey struct {
}

func NewContext(ctx context.Context, md Meta) context.Context {
	return context.WithValue(ctx, ctxKey{}, md)
}
func Metadata(ctx context.Context) Meta {
	v := ctx.Value(ctxKey{})
	if v == nil {
		return Meta{}
	}
	return v.(Meta)
}
