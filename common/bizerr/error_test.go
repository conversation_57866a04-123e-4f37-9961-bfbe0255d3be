package bizerr

import (
	"errors"
	"fmt"
	"testing"

	pkgerr "github.com/pkg/errors"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

type WrapError struct {
	err *BizError
	msg string
}

func (e *WrapError) Error() string {
	return e.err.Error()
}

func (e *WrapError) Unwrap() error {
	return e.err
}

func TestError(t *testing.T) {
	code := int32(12345)
	err := New(code, "error")
	assert.Equal(t, code, err.StatusCode())
	assert.Equal(t, "error", err.StatusMessage())
	assert.True(t, err.HasSameCode(err))
	assert.False(t, err.HasSameCode(errors.New("xxxx")))

	err2 := err.WithMessage("x")
	assert.Equal(t, "error", err.StatusMessage())
	assert.Equal(t, "x", err2.StatusMessage())
	assert.True(t, err2.HasSameCode(err))

	err3 := err.WithMessagef("err: %d %d", 1, 2)
	assert.Equal(t, "err: 1 2", err3.StatusMessage())

	e := &WrapError{err, "test"}
	assert.True(t, err.HasSameCode(e))
	assert.False(t, err.HasSameCode(fmt.Errorf("err: %w", errors.New("test"))))
}

func TestWrap(t *testing.T) {
	var bizErrType *BizError
	e1 := New(101, "e1")
	assert.Nil(t, e1.Unwrap())
	assert.ErrorAs(t, e1, &bizErrType)

	err := fmt.Errorf("err")
	e1 = e1.Wrap(err)
	assert.Equal(t, err, e1.Unwrap())
	assert.ErrorAs(t, e1, &bizErrType)
	unwrap, ok := UnwrapForBizErr(e1)
	assert.True(t, ok)
	assert.Equal(t, unwrap, e1)

	// 非 BizErr 无法解包
	unwrap, ok = UnwrapForBizErr(err)
	assert.False(t, ok)

	// 不能 wrap 自己
	e2 := New(102, "e2")
	e3 := e2.Wrap(e2)
	assert.Equal(t, e2, e3)
	assert.Nil(t, e2.Unwrap())

	// wrap 返回新的对象
	e4 := e2.Wrap(errors.New(""))
	assert.NotEqual(t, e2, e4)

	e5 := New(103, "e5")
	e5 = e5.Wrap(New(104, "e6"))
	unwrap, ok = UnwrapForBizErr(fmt.Errorf("xx: %w", fmt.Errorf("yy: %w", e5)))
	assert.True(t, ok)
	assert.Equal(t, e5, unwrap)

	e6 := New(104, "e6")
	wrapped := fmt.Errorf("err: %w", e6)
	for i := 0; i <= 120; i++ {
		wrapped = fmt.Errorf("err: %w", wrapped)
	}
	assert.ErrorAs(t, wrapped, &bizErrType)
	unwrap, ok = UnwrapForBizErr(wrapped)
	assert.False(t, ok)
}

type BigErr struct {
	*BizError
}

func (b *BigErr) Unwrap() error {
	return b.BizError
}
func TestCast(t *testing.T) {
	assert.True(t, raise() != nil)
	assert.True(t, IsNil(raise()))
	assert.True(t, IsNil(nil))

	bizErr, ok := CastBizErr(raise())
	assert.False(t, ok)
	assert.Nil(t, bizErr)

	bizErr, ok = CastBizErr(errors.New("aa"))
	assert.False(t, ok)

	bizErr, ok = CastBizErr(New(123, "xx"))
	assert.True(t, ok)
	assert.NotNil(t, bizErr)

	bizErr, ok = CastBizErr(&BigErr{New(123, "xx")})
	assert.False(t, ok)
	// assert.NotNil(t, bizErr)

	bizErr, ok = UnwrapForBizErr(fmt.Errorf("err: %w", raise()))
	assert.False(t, ok)
	assert.Nil(t, bizErr)

	bizErr, ok = UnwrapForBizErr(fmt.Errorf("err: %w", New(123, "xx")))
	assert.True(t, ok)
	assert.NotNil(t, bizErr)
}

func raise() error {
	var e *BizError
	return e
}

func TestBizError_Is(t *testing.T) {
	convey.Convey("wrap", t, func() {
		var e1 error
		e1 = New(429, "limit")
		for i := 0; i < 10; i++ {
			e1 = pkgerr.Wrapf(e1, "wrap %d", i+1)
		}
		// different http code
		convey.So(errors.Is(e1, NewHTTP(429, "limit", 503)), convey.ShouldBeTrue)
	})
}

func TestBizError_MarshalJSON(t *testing.T) {
	// Test normal BizError
	err := New(1001, "test error")
	data, jsonErr := err.MarshalJSON()
	assert.NoError(t, jsonErr)
	expected := `{"code":1001,"message":"test error"}`
	assert.JSONEq(t, expected, string(data))

	// Test nil BizError
	var nilErr *BizError
	data, jsonErr = nilErr.MarshalJSON()
	assert.NoError(t, jsonErr)
	assert.Equal(t, "null", string(data))
}
