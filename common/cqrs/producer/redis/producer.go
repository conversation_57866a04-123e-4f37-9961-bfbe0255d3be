package redis

import (
	"context"
	"fmt"
	"time"

	"hotel/common/log"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

// ProducerConfig 生产者配置
type ProducerConfig struct {
	MaxLen int64 `yaml:"max_len" json:"maxLen"` // Redis Stream最大长度
}

// PublishOptions 发布选项
type PublishOptions struct {
	Headers map[string]string `json:"headers"` // 消息头
}

// Producer Redis Stream生产者实现
type Producer struct {
	redis  *redis.Redis
	maxLen int64
}

// NewProducer 创建Redis Stream生产者
func NewProducer(redisConf redis.RedisConf, producerConfig *ProducerConfig) (*Producer, error) {
	// 创建Redis客户端
	redisClient, err := redis.NewRedis(redisConf)
	if err != nil {
		return nil, fmt.Errorf("failed to create redis client: %w", err)
	}

	maxLen := int64(10000) // 默认值
	if producerConfig != nil && producerConfig.MaxLen > 0 {
		maxLen = producerConfig.MaxLen
	}

	return &Producer{
		redis:  redisClient,
		maxLen: maxLen,
	}, nil
}

// Publish 发布消息到指定Stream
func (p *Producer) Publish(ctx context.Context, topic string, message []byte) error {
	return p.PublishWithOptions(ctx, topic, message, nil)
}

// PublishWithOptions 带选项发布消息
func (p *Producer) PublishWithOptions(ctx context.Context, topic string, message []byte, opts *PublishOptions) error {
	if topic == "" {
		return fmt.Errorf("topic name cannot be empty")
	}
	if ctx == nil {
		ctx = context.Background()
	}

	// 构建消息字段
	fields := map[string]interface{}{
		"body":      string(message), // 直接使用字符串，简化处理
		"timestamp": time.Now().Unix(),
	}

	// 添加头部信息
	if opts != nil && len(opts.Headers) > 0 {
		for k, v := range opts.Headers {
			fields["header_"+k] = v
		}
	}

	// 使用简单的 XADD 命令
	args := []interface{}{topic}
	if p.maxLen > 0 {
		args = append(args, "MAXLEN", "~", p.maxLen)
	}
	args = append(args, "*") // 自动生成ID

	// 添加字段
	for k, v := range fields {
		args = append(args, k, v)
	}

	_, err := p.redis.EvalCtx(ctx, `
		return redis.call('XADD', unpack(ARGV))
	`, nil, args...)

	if err != nil {
		log.Errorc(ctx, "Failed to publish message to stream %s: %v", topic, err)
		return fmt.Errorf("failed to publish message: %w", err)
	}

	log.Infoc(ctx, "Message published to stream %s", topic)
	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	// go-zero的Redis客户端不需要手动关闭
	log.Info("Redis Stream producer closed")
	return nil
}
