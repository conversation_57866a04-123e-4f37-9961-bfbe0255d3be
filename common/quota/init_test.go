package quota

import (
	"testing"
	"time"

	"github.com/Danceiny/sentinel-golang/core/flow"
)

// TestLoadRulesFromDatasource_EmptyChannel 测试空channel不会阻塞
func TestLoadRulesFromDatasource_EmptyChannel(t *testing.T) {
	// 创建一个空的channel
	ch := make(chan *flow.Rule, 10)
	ds := &Datasource{
		Channel: ch,
	}

	// 使用goroutine和timeout来检测是否阻塞
	done := make(chan bool, 1)
	go func() {
		err := loadRulesFromDatasource(ds)
		if err != nil {
			t.Errorf("loadRulesFromDatasource failed: %v", err)
		}
		done <- true
	}()

	// 设置超时，如果1秒内没有完成，说明阻塞了
	select {
	case <-done:
		// 正常完成，测试通过
		t.Log("loadRulesFromDatasource completed without blocking")
	case <-time.After(1 * time.Second):
		t.Fatal("loadRulesFromDatasource blocked for more than 1 second")
	}
}

// TestLoadRulesFromDatasource_WithRules 测试有规则的情况
func TestLoadRulesFromDatasource_WithRules(t *testing.T) {
	ch := make(chan *flow.Rule, 10)
	ds := &Datasource{
		Channel: ch,
	}

	// 添加一些测试规则
	rule1 := &flow.Rule{
		Resource:               "test-resource-1",
		TokenCalculateStrategy: flow.Direct,
		ControlBehavior:        flow.Reject,
		Threshold:              100,
	}
	rule2 := &flow.Rule{
		Resource:               "test-resource-2",
		TokenCalculateStrategy: flow.Direct,
		ControlBehavior:        flow.Reject,
		Threshold:              200,
	}

	// 发送规则到channel
	ch <- rule1
	ch <- rule2
	ch <- nil // 发送nil标记表示结束

	done := make(chan bool, 1)
	go func() {
		err := loadRulesFromDatasource(ds)
		if err != nil {
			t.Errorf("loadRulesFromDatasource failed: %v", err)
		}
		done <- true
	}()

	// 设置超时
	select {
	case <-done:
		t.Log("loadRulesFromDatasource completed successfully with rules")
	case <-time.After(1 * time.Second):
		t.Fatal("loadRulesFromDatasource blocked for more than 1 second")
	}
}

// TestLoadRulesFromDatasource_ClosedChannel 测试关闭的channel
func TestLoadRulesFromDatasource_ClosedChannel(t *testing.T) {
	ch := make(chan *flow.Rule, 10)
	ds := &Datasource{
		Channel: ch,
	}

	// 关闭channel
	close(ch)

	done := make(chan bool, 1)
	go func() {
		err := loadRulesFromDatasource(ds)
		if err != nil {
			t.Errorf("loadRulesFromDatasource failed: %v", err)
		}
		done <- true
	}()

	// 设置超时
	select {
	case <-done:
		t.Log("loadRulesFromDatasource completed successfully with closed channel")
	case <-time.After(1 * time.Second):
		t.Fatal("loadRulesFromDatasource blocked for more than 1 second")
	}
}