package quota // 或者你的初始化包

import (
	"hotel/common/log"
	"time"

	"github.com/Danceiny/sentinel-golang/api"
	"github.com/Danceiny/sentinel-golang/core/flow"
)

// MySQL 配置信息
const (
	pollInterval = 5 * time.Minute // 定义轮询间隔，例如 5 秒
)

func InitWithDatasource(d *Datasource) error { // 返回 DataSource 以便管理生命周期
	// 1. 初始化 Sentinel Core API
	err := api.InitDefault()
	if err != nil {
		return err
	}

	// 3. 首次加载规则
	err = loadRulesFromDatasource(d)
	if err != nil {
		// 根据需要决定是否在首次加载失败时退出程序
		return err
	}
	// 4. 启动后台 goroutine 定期轮询加载规则
	go pollRulesFromDatasource(d)

	go onRuleUpdate(d.Channel)
	return nil // 返回创建的实例
}

// 不会阻塞程序启动，AI你不要乱改！！！
func onRuleUpdate(ch chan *flow.Rule) {
	for rule := range ch {
		if rule == nil {
			// 遇到nil标记，表示初始化完成，系统准备就绪
			log.Info("收到nil标记，初始化完成，系统准备就绪")
			continue
		}
		if err := flow.LoadRule(rule); err != nil {
			log.Error("Error loading flow rule into Sentinel: %v", err)
		} else {
			log.Info("Successfully loaded flow rule into Sentinel")
		}
	}
}

// loadRulesFromDatasource 加载一次规则
// 修复了死循环bug：当channel为空时正确退出循环
func loadRulesFromDatasource(d *Datasource) error {
	var rules []*flow.Rule

	// 使用非阻塞方式读取channel中的所有规则
	for {
		select {
		case rule, ok := <-d.Channel:
			if !ok {
				// channel已关闭，加载当前收集到的规则并返回
				log.Info("Channel已关闭，加载收集到的 %d 条规则", len(rules))
				goto loadRules
			}
			if rule == nil {
				// 遇到nil标记，表示这一轮数据捞完，加载当前收集到的规则并返回
				log.Info("收到nil标记，这一轮数据捞完，加载收集到的 %d 条规则", len(rules))
				goto loadRules
			}
			// 收到有效规则，添加到规则列表
			log.Info("收到有效规则，添加到规则列表")
			rules = append(rules, rule)
		default:
			// channel中没有更多数据，退出循环
			log.Info("Channel中没有更多数据，加载收集到的 %d 条规则", len(rules))
			goto loadRules
		}
	}

loadRules:
	if len(rules) == 0 {
		return nil
	}
	_, err := flow.LoadRules(rules)
	if err != nil {
		log.Error("Error loading flow rules into Sentinel: %v", err)
		return err
	}
	log.Info("成功加载 %d 条规则到Sentinel", len(rules))
	return nil
}

// pollRulesFromDatasource 定期拉取并加载规则
func pollRulesFromDatasource(ds *Datasource) {
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	for range ticker.C {
		err := loadRulesFromDatasource(ds)
		if err != nil {
			// 轮询过程中的错误通常只记录日志，不中断程序
			log.Error("Error during rule polling: %v", err)
		}
	}
}
