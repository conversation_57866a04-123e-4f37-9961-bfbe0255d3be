package pdf

import (
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/jung-kurt/gofpdf"
)

// 内容块类型定义
const (
	BlockTypeTitle     = "title"
	BlockTypeText      = "text"
	BlockTypeBold      = "bold"
	BlockTypeItalic    = "italic"
	BlockTypeNewLine   = "newline"
	BlockTypeTable     = "table"
	BlockTypeList      = "list"
	BlockTypeImage     = "image"
	BlockTypePageBreak = "pagebreak"
	BlockTypeLine      = "line"
)

// FontStyle 字体样式定义
type FontStyle struct {
	Family    string  `json:"family"`
	Size      float64 `json:"size"`
	Style     string  `json:"style"`
	Color     [3]int  `json:"color"`
	Underline bool    `json:"underline"`
}

// ContentBlock 内容块定义
type ContentBlock struct {
	Type        string     `json:"type"`
	Content     string     `json:"content"`
	Font        *FontStyle `json:"font"`
	LineHeight  float64    `json:"lineHeight"`
	Align       string     `json:"align"`
	TableData   [][]string `json:"tableData"`
	ImagePath   string     `json:"imagePath"`
	ImageWidth  float64    `json:"imageWidth"`
	ImageHeight float64    `json:"imageHeight"`
	ListItems   []string   `json:"listItems"`
	LineWidth   float64    `json:"lineWidth"`
	LineColor   [3]int     `json:"lineColor"`
	BulletType  string     `json:"bulletType"` // "circle", "square", "dash" 或 "none"
}

// PDFTemplate PDF模板结构体
type PDFTemplate struct {
	Title         string         `json:"title"`
	Author        string         `json:"author"`
	Subject       string         `json:"subject"`
	DefaultFont   FontStyle      `json:"defaultFont"`
	PageSize      string         `json:"pageSize"`
	Orientation   string         `json:"orientation"`
	Margins       [4]float64     `json:"margins"` // [left, top, right, bottom]
	ContentBlocks []ContentBlock `json:"contentBlocks"`
	FontDir       string         `json:"fontDir"`
	TextEncoding  string         `json:"textEncoding"` // "utf8" 或 "latin1"
}

// GeneratePDF 生成PDF文件
func GeneratePDF(template PDFTemplate, outputPath string) error {
	if err := validateTemplate(template); err != nil {
		return err
	}

	// 初始化PDF文档
	pdf := gofpdf.New(template.Orientation, "mm", template.PageSize, template.FontDir)

	// 设置文本编码转换器
	tr := pdf.UnicodeTranslatorFromDescriptor("") // 默认转换器
	if template.TextEncoding == "latin1" {
		tr = pdf.UnicodeTranslatorFromDescriptor("latin1")
	}

	// 添加中文字体支持
	if template.FontDir != "" {
		// 注册简体中文字体
		if err := registerChineseFont(pdf, template.FontDir, "SimSun"); err != nil {
			// 如果无法注册中文字体，记录警告但不中断执行
			fmt.Printf("警告：无法注册中文字体 SimSun: %v\n", err)
		}
		if err := registerChineseFont(pdf, template.FontDir, "SimHei"); err != nil {
			fmt.Printf("警告：无法注册中文字体 SimHei: %v\n", err)
		}
		
		// 尝试注册系统中常见的中文字体
		systemFonts := []string{
			"NotoSansCJK",     // Google Noto 字体
			"SourceHanSans",   // Adobe Source Han Sans
			"PingFang",        // macOS 系统字体
			"Microsoft YaHei", // Windows 系统字体
		}
		
		for _, fontName := range systemFonts {
			if err := registerChineseFont(pdf, template.FontDir, fontName); err == nil {
				// 成功注册一个字体后就停止尝试
				break
			}
		}
	}

	// 设置文档信息
	pdf.SetTitle(template.Title, false)
	pdf.SetAuthor(template.Author, false)
	pdf.SetSubject(template.Subject, false)

	// 添加页面
	pdf.AddPage()

	// 设置默认字体
	SetFontWithChineseSupport(pdf, template.DefaultFont, template.FontDir)

	// 设置页边距
	pdf.SetMargins(template.Margins[0], template.Margins[1], template.Margins[2])
	pdf.SetAutoPageBreak(true, template.Margins[3])

	// 处理所有内容块
	for _, block := range template.ContentBlocks {
		if err := processContentBlock(pdf, block, template.DefaultFont, template.FontDir, tr); err != nil {
			return fmt.Errorf("处理内容块时出错: %w", err)
		}
	}

	// 保存PDF文件
	return pdf.OutputFileAndClose(outputPath)
}

// 注册中文字体及其变体
func registerChineseFont(pdf *gofpdf.Fpdf, fontDir, fontName string) error {
	fonts := map[string]string{
		"":    fontName + ".json",
		"B":   fontName + "b.json",
		"I":   fontName + "i.json",
		"BI":  fontName + "bi.json",
		"U":   fontName + ".json",
		"BU":  fontName + "b.json",
		"IU":  fontName + "i.json",
		"BIU": fontName + "bi.json",
	}

	// 检查至少一个字体文件是否存在
	baseFont := fontDir + "/" + fonts[""]
	if _, err := os.Stat(baseFont); os.IsNotExist(err) {
		return fmt.Errorf("字体文件不存在: %s", baseFont)
	}

	// 注册所有存在的字体变体
	for style, file := range fonts {
		fontFile := fontDir + "/" + file
		if _, err := os.Stat(fontFile); err == nil {
			pdf.AddFont(fontName, style, file)
		}
	}

	return nil
}

// GetRecommendedChineseFont 获取推荐的中文字体
func GetRecommendedChineseFont(fontDir string) string {
	// 按优先级排序的中文字体列表
	fonts := []string{
		"NotoSansCJK-Regular",    // Google Noto，最广泛支持
		"SourceHanSans-Regular",  // Adobe Source Han Sans
		"PingFang-Regular",       // macOS 苹方字体
		"Microsoft-YaHei",        // Windows 微软雅黑
		"SimSun",                 // 宋体
		"SimHei",                 // 黑体
	}

	if fontDir == "" {
		return fonts[0] // 返回默认推荐字体
	}

	// 检查字体目录中是否存在推荐字体
	for _, font := range fonts {
		fontFile := fontDir + "/" + font + ".json"
		if _, err := os.Stat(fontFile); err == nil {
			return font
		}
	}

	return fonts[0] // 如果都不存在，返回默认字体
}

// IsChinese 检查文本是否包含中文字符
func IsChinese(text string) bool {
	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// SetFontWithChineseSupport 设置支持中文的字体
func SetFontWithChineseSupport(pdf *gofpdf.Fpdf, font FontStyle, fontDir string) {
	fontFamily := font.Family
	
	// 如果字体家族为空或者是默认字体，且文本可能包含中文，则使用推荐的中文字体
	if fontFamily == "" || fontFamily == "Arial" || fontFamily == "Helvetica" {
		fontFamily = GetRecommendedChineseFont(fontDir)
	}
	
	pdf.SetFont(fontFamily, font.Style, font.Size)
	pdf.SetTextColor(font.Color[0], font.Color[1], font.Color[2])

	// 处理下划线
	if font.Underline {
		pdf.SetFontStyle(font.Style + "U")
	} else {
		pdf.SetFontStyle(font.Style)
	}
}

// validateTemplate 验证模板配置
func validateTemplate(template PDFTemplate) error {
	validSizes := map[string]bool{
		"A3": true, "A4": true, "A5": true, "Letter": true, "Legal": true,
	}
	if !validSizes[template.PageSize] {
		return errors.New("无效的页面尺寸")
	}

	if template.Orientation != "P" && template.Orientation != "L" {
		return errors.New("无效的页面方向 (必须是 'P' 或 'L')")
	}

	for _, margin := range template.Margins {
		if margin < 0 {
			return errors.New("页边距不能为负值")
		}
	}

	if template.DefaultFont.Size <= 0 {
		return errors.New("默认字体大小必须大于0")
	}

	return nil
}

// processContentBlock 处理单个内容块
func processContentBlock(pdf *gofpdf.Fpdf, block ContentBlock, defaultFont FontStyle, fontDir string, tr func(string) string) error {
	// 获取当前页面宽度
	pageWidth, _ := pdf.GetPageSize()

	// 获取左边距和右边距
	leftMargin, _, rightMargin, _ := pdf.GetMargins()
	availableWidth := pageWidth - leftMargin - rightMargin

	// 确定字体样式
	fontStyle := defaultFont
	if block.Font != nil {
		fontStyle = *block.Font
	}

	// 设置行高
	lineHeight := fontStyle.Size * 1.5
	if block.LineHeight > 0 {
		lineHeight = block.LineHeight
	}

	// 设置对齐方式
	align := "L"
	if block.Align != "" {
		align = block.Align
	}

	// 处理不同类型的内容块
	switch block.Type {
	case BlockTypeTitle:
		SetFontWithChineseSupport(pdf, fontStyle, fontDir)
		pdf.Ln(lineHeight * 1.5)
		// 使用编码转换器处理标题文本
		pdf.CellFormat(0, lineHeight, tr(block.Content), "", 1, align, false, 0, "")
		pdf.Ln(lineHeight * 0.5)

	case BlockTypeText, BlockTypeBold, BlockTypeItalic:
		SetFontWithChineseSupport(pdf, fontStyle, fontDir)

		// 应用特定样式
		originalStyle := fontStyle.Style
		switch block.Type {
		case BlockTypeBold:
			fontStyle.Style = addStyle(fontStyle.Style, "B")
		case BlockTypeItalic:
			fontStyle.Style = addStyle(fontStyle.Style, "I")
		}

		if fontStyle.Style != originalStyle {
			pdf.SetFontStyle(fontStyle.Style)
		}

		// 多行文本处理，使用编码转换器
		pdf.MultiCell(0, lineHeight, tr(block.Content), "", align, false)
		pdf.Ln(lineHeight * 0.3)

		// 重置字体样式
		if fontStyle.Style != originalStyle {
			pdf.SetFontStyle(originalStyle)
		}

	case BlockTypeNewLine:
		pdf.Ln(lineHeight)

	case BlockTypeTable:
		if len(block.TableData) == 0 {
			return nil
		}

		// 计算列宽
		colCount := len(block.TableData[0])
		colWidth := availableWidth / float64(colCount)

		// 设置表格样式
		headerFont := fontStyle
		headerFont.Style = addStyle(headerFont.Style, "B")
		headerFont.Color = [3]int{0, 0, 0}

		// 绘制表头
		SetFontWithChineseSupport(pdf, headerFont, fontDir)
		for _, col := range block.TableData[0] {
			// 使用编码转换器处理表头文本
			pdf.CellFormat(colWidth, lineHeight, tr(col), "1", 0, "C", false, 0, "")
		}
		pdf.Ln(-1)

		// 绘制表格内容
		SetFontWithChineseSupport(pdf, fontStyle, fontDir)
		for i := 1; i < len(block.TableData); i++ {
			if len(block.TableData[i]) != colCount {
				return fmt.Errorf("表格第%d行列数不匹配", i+1)
			}

			for _, col := range block.TableData[i] {
				// 使用编码转换器处理表格内容
				pdf.CellFormat(colWidth, lineHeight, tr(col), "1", 0, "L", false, 0, "")
			}
			pdf.Ln(-1)
		}
		pdf.Ln(lineHeight)

	case BlockTypeList:
		SetFontWithChineseSupport(pdf, fontStyle, fontDir)

		startX := pdf.GetX()
		bulletWidth := fontStyle.Size * 0.5

		for _, item := range block.ListItems {
			// 绘制项目符号（使用图形而非字符）
			drawBullet(pdf, block.BulletType, startX, pdf.GetY(), lineHeight, fontStyle)

			// 设置文本位置
			pdf.SetX(startX + bulletWidth + 2)

			// 使用编码转换器处理列表项文本
			pdf.MultiCell(0, lineHeight, tr(item), "", align, false)
			pdf.SetX(startX)
			pdf.Ln(lineHeight * 0.5)
		}
		pdf.Ln(lineHeight * 0.5)

	case BlockTypeImage:
		if block.ImagePath == "" {
			return errors.New("图片路径不能为空")
		}

		// 检查图片是否存在
		if _, err := os.Stat(block.ImagePath); os.IsNotExist(err) {
			return fmt.Errorf("图片文件不存在: %s", block.ImagePath)
		}

		options := gofpdf.ImageOptions{
			ImageType: "",
		}

		pdf.Ln(lineHeight)

		// 计算图片高度
		imgWidth, imgHeight := calculateImageDimensions(pdf, block)

		// 居中显示图片
		x := leftMargin + (availableWidth-imgWidth)/2
		pdf.ImageOptions(block.ImagePath, x, pdf.GetY(), imgWidth, imgHeight, false, options, 0, "")

		pdf.SetY(pdf.GetY() + imgHeight + lineHeight)

	case BlockTypePageBreak:
		pdf.AddPage()
		pdf.SetY(defaultFont.Size)

	case BlockTypeLine:
		drawHorizontalLine(pdf, block, lineHeight, leftMargin, availableWidth)

	default:
		return fmt.Errorf("未知的内容块类型: %s", block.Type)
	}

	return nil
}

// 绘制项目符号（替代字符方案）
func drawBullet(pdf *gofpdf.Fpdf, bulletType string, startX, startY, lineHeight float64, font FontStyle) {
	bulletRadius := font.Size * 0.15
	yPos := startY + lineHeight/2

	switch bulletType {
	case "circle":
		pdf.SetFillColor(font.Color[0], font.Color[1], font.Color[2])
		pdf.Circle(startX+bulletRadius, yPos, bulletRadius, "F")
		pdf.SetFillColor(0, 0, 0) // 重置为黑色

	case "square":
		size := bulletRadius * 1.5
		pdf.SetFillColor(font.Color[0], font.Color[1], font.Color[2])
		pdf.Rect(startX, yPos-bulletRadius, size, size, "F")
		pdf.SetFillColor(0, 0, 0)

	case "dash":
		dashLength := font.Size * 0.4
		pdf.SetLineWidth(font.Size * 0.1)
		pdf.Line(startX, yPos, startX+dashLength, yPos)
		pdf.SetLineWidth(0.2) // 重置线宽

	default: // "none" 或未指定
		// 不绘制项目符号
	}
}

// 计算图片尺寸
func calculateImageDimensions(pdf *gofpdf.Fpdf, block ContentBlock) (float64, float64) {
	imgWidth := block.ImageWidth
	imgHeight := block.ImageHeight

	// 如果未指定尺寸，尝试获取图片原始尺寸
	if imgWidth <= 0 || imgHeight <= 0 {
		info := pdf.GetImageInfo(block.ImagePath)
		if info != nil {
			if imgWidth <= 0 {
				imgWidth = info.Width()
			}
			if imgHeight <= 0 {
				imgHeight = info.Height()
			}
		} else {
			// 默认尺寸
			if imgWidth <= 0 {
				imgWidth = 100
			}
			if imgHeight <= 0 {
				imgHeight = 80
			}
		}
	}

	return imgWidth, imgHeight
}

// 绘制水平线
func drawHorizontalLine(pdf *gofpdf.Fpdf, block ContentBlock, lineHeight, leftMargin, availableWidth float64) {
	lineWidth := block.LineWidth
	if lineWidth <= 0 {
		lineWidth = 0.2
	}

	// 设置线条颜色
	if block.LineColor[0] != 0 || block.LineColor[1] != 0 || block.LineColor[2] != 0 {
		pdf.SetDrawColor(block.LineColor[0], block.LineColor[1], block.LineColor[2])
	}

	// 设置线宽
	pdf.SetLineWidth(lineWidth)

	// 绘制线条
	y := pdf.GetY() + lineHeight/2
	pdf.Line(leftMargin, y, leftMargin+availableWidth, y)

	// 重置绘图设置
	pdf.SetDrawColor(0, 0, 0)
	pdf.SetLineWidth(0.2)

	pdf.SetY(y + lineHeight)
}

// setFont 设置PDF字体
func setFont(pdf *gofpdf.Fpdf, font FontStyle) {
	if font.Family != "" {
		pdf.SetFont(font.Family, font.Style, font.Size)
	}

	pdf.SetTextColor(font.Color[0], font.Color[1], font.Color[2])

	// 处理下划线
	if font.Underline {
		pdf.SetFontStyle(font.Style + "U")
	} else {
		pdf.SetFontStyle(font.Style)
	}
}

// addStyle 添加样式
func addStyle(existing, newStyle string) string {
	if strings.Contains(existing, newStyle) {
		return existing
	}
	return existing + newStyle
}

// CreateChinesePDFExample 创建一个中文PDF示例
func CreateChinesePDFExample(fontDir, outputPath string) error {
	template := PDFTemplate{
		Title:       "中文PDF示例",
		Author:      "HotelByte系统",
		Subject:     "中文字体支持测试",
		PageSize:    "A4",
		Orientation: "P",
		Margins:     [4]float64{20, 20, 20, 20},
		FontDir:     fontDir,
		DefaultFont: FontStyle{
			Family: "", // 将由系统自动选择合适的中文字体
			Size:   12,
			Style:  "",
			Color:  [3]int{0, 0, 0},
		},
		TextEncoding: "utf8",
		ContentBlocks: []ContentBlock{
			{
				Type:    BlockTypeTitle,
				Content: "酒店预订系统中文支持",
				Font: &FontStyle{
					Family: "",
					Size:   18,
					Style:  "B",
					Color:  [3]int{0, 0, 139},
				},
				Align: "C",
			},
			{
				Type:    BlockTypeText,
				Content: "本文档演示了PDF生成器对中文字符的完整支持能力。包括简体中文、繁体中文以及各种标点符号的正确显示。",
			},
			{
				Type:    BlockTypeText,
				Content: "支持的功能包括：标题、正文、粗体、斜体、表格、列表等各种内容类型。",
				Font: &FontStyle{
					Size:  14,
					Style: "B",
				},
			},
			{
				Type: BlockTypeTable,
				TableData: [][]string{
					{"功能", "状态", "备注"},
					{"中文显示", "✓ 支持", "完全支持简繁体中文"},
					{"字体自动选择", "✓ 支持", "自动选择最佳中文字体"},
					{"多种样式", "✓ 支持", "粗体、斜体等"},
				},
			},
			{
				Type: BlockTypeList,
				ListItems: []string{
					"支持多种中文字体：宋体、黑体、微软雅黑等",
					"自动检测系统字体并选择最佳匹配",
					"完整的Unicode支持",
					"兼容各种操作系统",
				},
				BulletType: "circle",
			},
		},
	}

	return GeneratePDF(template, outputPath)
}
