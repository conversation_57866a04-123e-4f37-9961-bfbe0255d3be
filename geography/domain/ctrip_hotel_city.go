package domain

import (
	"fmt"

	"hotel/common/idgen"
	"hotel/common/types"

	"github.com/spf13/cast"
)

// {"countryId":98,"name":"新西兰","enName":"New Zealand","code":"NZ","continentId":3,"continentName":"大洋洲","areaCode":"64"}
func (c *CtripPOIData) ToRegion(countryRegion *Region) *Region {
	cities := make([]*Region, 0)
	for _, city := range c.PrefectureLevelCityInfoList {
		counties := make([]*Region, 0)
		for _, county := range city.CountyList {
			region := &Region{
				ID:       idgen.String2Int64ID(cast.ToString(county.CountyID)),
				Type:     RegionType_City,
				Name:     county.CountyEnName,
				NameFull: fmt.Sprintf("%s,%s,%s,%s", county.CountyEnName, city.CityEnName, c.ProvinceEnName, countryRegion.Name),
				SupplierID: SupplierID{
					TripId: cast.ToString(county.CountyID),
				},
				CountryCode:            countryRegion.CountryCode,
				CountrySubdivisionCode: countryRegion.CountrySubdivisionCode,
				Coordinates:            Coordinates{},
				Ancestors:              nil,
				Descendants:            nil,
				Extra: Extra{
					NameZh:        county.CountyName,
					CityName:      city.CityEnName,
					ProvinceName:  c.ProvinceEnName,
					CountryName:   countryRegion.Name,
					CountryNameZh: countryRegion.Extra.NameZh,
				},
			}
			// 在数据构建时优化NameFull
			region.OptimizeNameFull()
			counties = append(counties, region)
		}
		cityRegion := &Region{
			ID:       idgen.String2Int64ID(cast.ToString(city.CityID)),
			Type:     RegionType_MultiCityVicinity,
			Name:     city.CityEnName,
			NameFull: fmt.Sprintf("%s,%s,%s", city.CityEnName, c.ProvinceEnName, countryRegion.Name),
			SupplierID: SupplierID{
				TripId: cast.ToString(city.CityID),
			},
			CountryCode:            countryRegion.CountryCode,
			CountrySubdivisionCode: countryRegion.CountrySubdivisionCode,
			Coordinates:            Coordinates{},
			Ancestors:              nil,
			Descendants:            counties,
			Extra: Extra{
				NameZh:        city.CityName,
				ProvinceName:  c.ProvinceEnName,
				CountryName:   countryRegion.Name,
				CountryNameZh: countryRegion.Extra.NameZh,
			},
		}
		// 在数据构建时优化NameFull
		cityRegion.OptimizeNameFull()
		cities = append(cities, cityRegion)
	}
	provinceRegion := &Region{
		ID:       types.ID(CtripProvinceID2RegionID(c.ProvinceID)),
		Type:     RegionType_ProvinceState,
		Name:     c.ProvinceEnName,
		NameFull: fmt.Sprintf("%s,%s", c.ProvinceEnName, countryRegion.Name),
		SupplierID: SupplierID{
			TripId: cast.ToString(c.ProvinceID),
		},
		CountryCode:            countryRegion.CountryCode,
		CountrySubdivisionCode: countryRegion.CountrySubdivisionCode,
		Coordinates:            Coordinates{},
		Descendants:            cities,
		Extra: Extra{
			NameZh:        c.ProvinceName,
			ProvinceName:  c.ProvinceEnName,
			CountryName:   countryRegion.Name,
			CountryNameZh: countryRegion.Extra.NameZh,
		},
	}
	// 在数据构建时优化NameFull
	provinceRegion.OptimizeNameFull()
	return provinceRegion
}
func (c *CtripCountryBaseInfo) ToRegion() *Region {
	return &Region{
		ID:       types.ID(CtripCountryID2RegionID(c.CountryId)),
		Type:     RegionType_Country,
		Name:     c.EnName,
		NameFull: c.EnName,
		SupplierID: SupplierID{
			TripId: cast.ToString(c.CountryId),
		},
		CountryCode:            c.Code,
		CountrySubdivisionCode: "",
		Coordinates:            Coordinates{},
		Ancestors:              nil,
		Descendants:            nil,
		Extra: Extra{
			NameZh:          c.Name,
			ContinentNameZh: c.ContinentName,
		},
	}
}

// ctrip的city、province、country没有共用一套ID，需要强行映射一波
func CtripCountryID2RegionID(v int64) int64 {
	return 1000000 + v
}

// ctrip的city、province、country没有共用一套ID，需要强行映射一波
func CtripProvinceID2RegionID(v int64) int64 {
	return 2000000 + v
}
