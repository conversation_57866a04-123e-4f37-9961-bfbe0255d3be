package service

import (
	"context"
	"slices"
	"sync"
	"time"

	"github.com/avast/retry-go/v4"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/bizerr"
	"hotel/common/fanout"
	"hotel/common/log"
	"hotel/common/types"

	confutil "hotel/common/config"
	"hotel/geography/config"
	"hotel/geography/domain"
	"hotel/geography/mysql"
	"hotel/geography/protocol"
	supplierDomain "hotel/supplier/domain"
)

var configFile = confutil.SafeFlagString("geography", "geography/config/config.yaml", "the geo config file")

type GeographyService struct {
	googleMapsSrv  *GoogleMapsService
	hierarchySrv   *HierarchySearchService
	fuzzySearchSrv *BleveSearchService
	fan            *fanout.Fanout
	regionDao      *mysql.RegionDao
	indexManager   *InMemoryIndexManager
}

func (s *GeographyService) UnhealthyReason() string {
	if s.fuzzySearchSrv == nil {
		return "LOADING"
	}
	return ""
}

var (
	_geography *GeographyService
	_once      sync.Once
)

func NewGeographyService() *GeographyService {
	_once.Do(func() {
		var cfg config.Config
		conf.MustLoad(*configFile, &cfg)

		geoDB := sqlx.NewMysql(cfg.MySQL.Geography)
		repo := mysql.NewRegionDao(geoDB)

		gms := NewGoogleMapsService(cfg.GoogleMaps)
		imindex := NewInMemoryIndexManager()
		// 初始化层级服务
		hierarchyService := NewHierarchySearchService(imindex)
		_geography = &GeographyService{
			googleMapsSrv: gms,
			hierarchySrv:  hierarchyService,
			fan:           fanout.New("GeographyService"),
			regionDao:     repo,
			indexManager:  imindex,
		}
		_ = _geography.fan.Do(context.Background(), func(ctx context.Context) {
			var (
				st      = time.Now()
				regions []*domain.Region
				err     error
			)
			err = retry.Do(func() error {
				regions, err = repo.MonitoredLoadAllRegionsFromFile(context.Background())
				return err
			}, retry.Attempts(1), retry.Delay(time.Second))
			if err != nil {
				logx.Errorf("failed to load regions: %v", err)
			}
			log.Infoc(ctx, "LoadAllRegionsFromFile loaded %d regions", len(regions))
			// 构建索引
			imindex.BuildIndexes(regions)
			// 初始化搜索服务
			fuzzySearchSrv, err := NewBleveSearchService(imindex)
			if err != nil {
				logx.Errorf("NewBleveSearchService %v", err)
			}
			_geography.fuzzySearchSrv = fuzzySearchSrv
			log.Infoc(ctx, "NewGeographyService loaded %d regions, cost:%s", len(regions), time.Since(st))
		})
	})
	return _geography
}

func (s *GeographyService) GetRegion(ctx context.Context, id types.ID) (*domain.Region, error) {
	return s.hierarchySrv.Get(id)
}
func (s *GeographyService) GetRegionBySupplierRegionId(ctx context.Context, suplier supplierDomain.Supplier, regionId string) (*domain.Region, error) {
	v, ok := s.indexManager.GetRegionBySupplierRegionId(suplier, regionId)
	if !ok {
		return nil, bizerr.NotFoundErr
	}
	return v, nil
}

func (s *GeographyService) GetRegions(ctx context.Context, ids types.IDs) (map[types.ID]*domain.Region, error) {
	return s.hierarchySrv.MGet(ids)
}
func (s *GeographyService) GetDescendants(ctx context.Context, id types.ID) ([]*domain.Region, error) {
	return s.hierarchySrv.GetDescendants(id), nil
}

// GetExpandedRegionIds 获取扩展的region ID列表，包含原始region、所有祖先和所有后代
func (s *GeographyService) GetExpandedRegionIds(ctx context.Context, regionIds types.IDs) (types.IDs, error) {
	if len(regionIds) == 0 {
		return regionIds, nil
	}

	expandedIdsMap := make(map[types.ID]bool)

	// 添加原始region IDs
	for _, regionId := range regionIds {
		expandedIdsMap[regionId] = true
	}

	// 为每个region ID获取祖先和后代
	for _, regionId := range regionIds {
		// 获取祖先regions
		ancestors := s.hierarchySrv.GetAncestors(regionId)
		for _, ancestor := range ancestors {
			expandedIdsMap[ancestor.ID] = true
		}

		// 获取后代regions
		descendants := s.hierarchySrv.GetDescendants(regionId)
		for _, descendant := range descendants {
			expandedIdsMap[descendant.ID] = true
		}
	}

	// 转换map为slice
	expandedIds := make(types.IDs, 0, len(expandedIdsMap))
	for regionId := range expandedIdsMap {
		expandedIds = append(expandedIds, regionId)
	}

	log.Infoc(ctx, "GetExpandedRegionIds: original %d regions expanded to %d regions", len(regionIds), len(expandedIds))
	return expandedIds, nil
}
func (s *GeographyService) FuzzySearch(ctx context.Context, req *protocol.FuzzySearchReq) (*protocol.FuzzySearchResp, error) {
	regions, internalRes, err := s.fuzzySearchSrv.Search(ctx, req.Keyword, 1, 10)
	if err != nil {
		return nil, err
	}
	log.Infoc(ctx, "fuzzySearchSrv.Anything keyword(%v) internalRes(%v)", req.Keyword, internalRes)

	if len(regions) > 0 {
		vs := convertRegions2FuzzySearchItem(regions)
		var rs = make([]*protocol.FuzzySearchItem, 0, len(vs))
		for _, v := range vs {
			if len(req.RegionTypes) == 0 || slices.Contains(req.RegionTypes, v.Region.Type) {
				rs = append(rs, v)
			}
		}
		return &protocol.FuzzySearchResp{Candidates: rs}, nil
	}

	if slices.Contains(req.ContentTypes, protocol.FuzzySearchContentType_Place) {
		places, err := s.googleMapsSrv.SearchPlace(ctx, req.Keyword)
		if err != nil {
			return nil, err
		}
		log.Infoc(ctx, "googleMapsSrv.SearchPlace:%v", internalRes)

		return &protocol.FuzzySearchResp{
			Candidates: convertPlaces2FuzzySearchItem(places),
		}, nil
	}

	return &protocol.FuzzySearchResp{}, nil
}

// SyncLocalFilesToDB syncs local geography files to database with incremental updates
func (s *GeographyService) SyncLocalFilesToDB(ctx context.Context) (*mysql.SyncStats, error) {
	return s.regionDao.MonitoredSyncLocalFilesToDB(ctx)
}

func convertRegion2FuzzySearchItem(in *domain.Region) *protocol.FuzzySearchItem {
	return &protocol.FuzzySearchItem{
		Type:   protocol.FuzzySearchContentType_City,
		Region: in,
	}
}
func convertRegions2FuzzySearchItem(in []*domain.Region) []*protocol.FuzzySearchItem {
	out := make([]*protocol.FuzzySearchItem, len(in))
	for i := range in {
		out[i] = convertRegion2FuzzySearchItem(in[i])
	}
	return out
}
func convertPlace2FuzzySearchItem(in *domain.Place) *protocol.FuzzySearchItem {
	return &protocol.FuzzySearchItem{Place: in}
}
func convertPlaces2FuzzySearchItem(in []*domain.Place) []*protocol.FuzzySearchItem {
	out := make([]*protocol.FuzzySearchItem, len(in))
	for i := range in {
		out[i] = convertPlace2FuzzySearchItem(in[i])

	}
	return out
}
