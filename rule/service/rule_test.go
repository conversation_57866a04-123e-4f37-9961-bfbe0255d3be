package service

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/bytedance/arishem/typedef"

	"github.com/stretchr/testify/assert"

	commonDomain "hotel/common/domain"
	"hotel/rule/domain"
)

func TestRule(t *testing.T) {
	//todo: 字段名也是动态生成的动态取值似乎arishem 还不支持……
	ctx := context.Background()
	type Hotel struct {
		ID          int64 `json:"id"`
		SupplierId5 int64 `json:"supplierId5"`
		SupplierId6 int64 `json:"supplierId6"`
		SupplierId7 int64 `json:"supplierId7"`
		SupplierId8 int64 `json:"supplierId8"`
	}
	r := &domain.Rule{
		ID:        1,
		Name:      "supplier_id",
		Status:    commonDomain.StatusEnable,
		Condition: `{"OpLogic":"always","Conditions":[]}`,
		Aim:       `{"Operator":"GetField","Lhs":{"VarExpr":"hotel"},"Rhs":{"Operator":"StringConcat","Lhs":{"Const":{"StrConst":"supplier_id_"}},"Rhs":{"Operator":"ToString","Lhs":{"VarExpr":"supplier"}}}}`,
	}
	h := &Hotel{
		ID:          1,
		SupplierId5: 55,
		SupplierId6: 66,
	}
	ruleRes, err := r.RunWithMap(ctx, typedef.MetaType{
		"hotel":    h,
		"supplier": 5,
	})
	assert.NoError(t, err)
	assert.Equal(t, int64(55), ruleRes.Aim().AsExpr())

	ruleRes, err = r.RunWithMap(ctx, typedef.MetaType{
		"hotel":    h,
		"supplier": 6,
	})
	assert.NoError(t, err)
	assert.Equal(t, int64(66), ruleRes.Aim().AsExpr())
}

func TestRuleIntersect(t *testing.T) {
	// 初始化规则服务
	ruleService := NewRuleService()
	assert.NotNil(t, ruleService)

	// 测试用例1: 供应商列表交集
	t.Run("supplier_intersect_test", func(t *testing.T) {
		rule, err := ruleService.ruleDAO.GetRule(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, rule)

		// 测试数据：suppliers包含[5,6,7]，与规则中的[5,6,7,8]有交集
		testParams := map[string]interface{}{
			"suppliers":   []int{5, 6, 7},
			"supplier_id": 6,
		}

		paramsJSON, _ := json.Marshal(testParams)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		// 验证结果
		assert.True(t, result.Passed(), "供应商交集规则应该匹配")

		// 获取结果值
		value := result.Aim().AsExpr()
		assert.Equal(t, "include", value)
	})

	// 测试用例2: 城市列表交集
	t.Run("city_intersect_test", func(t *testing.T) {
		rule, err := ruleService.ruleDAO.GetRule(context.Background(), 2)
		assert.NoError(t, err)
		assert.NotNil(t, rule)

		// 测试数据：cities包含["北京", "上海"]，价格>=100
		testParams := map[string]interface{}{
			"cities": []string{"北京", "上海", "杭州"},
			"price":  150,
		}

		paramsJSON, _ := json.Marshal(testParams)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		assert.True(t, result.Passed(), "城市交集规则应该匹配")

		value := result.Aim().AsExpr()
		assert.Equal(t, "premium_city", value)
	})

	// 测试用例3: 酒店类型交集
	t.Run("hotel_type_intersect_test", func(t *testing.T) {
		rule, err := ruleService.ruleDAO.GetRule(context.Background(), 3)
		assert.NoError(t, err)
		assert.NotNil(t, rule)

		// 测试数据：酒店类型和设施都有交集
		testParams := map[string]interface{}{
			"hotel_types": []string{"商务酒店", "度假酒店"},
			"amenities":   []string{"WiFi", "健身房", "餐厅"},
		}

		paramsJSON, _ := json.Marshal(testParams)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		assert.True(t, result.Passed(), "酒店类型交集规则应该匹配")

		value := result.Aim().AsExpr()
		assert.Equal(t, "luxury_hotel", value)
	})

	// 测试用例4: 复杂交集条件
	t.Run("complex_intersect_test", func(t *testing.T) {
		rule, err := ruleService.ruleDAO.GetRule(context.Background(), 4)
		assert.NoError(t, err)
		assert.NotNil(t, rule)

		// 测试数据：满足所有条件
		testParams := map[string]interface{}{
			"suppliers":   []int{1, 2, 3},
			"cities":      []string{"北京", "上海"},
			"star_rating": 5,
		}

		paramsJSON, _ := json.Marshal(testParams)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		assert.True(t, result.Passed(), "复杂交集规则应该匹配")

		value := result.Aim().AsExpr()
		assert.Equal(t, "premium_service", value)
	})

	// 测试用例5: 空集交集
	t.Run("empty_intersect_test", func(t *testing.T) {
		rule, err := ruleService.ruleDAO.GetRule(context.Background(), 5)
		assert.NoError(t, err)
		assert.NotNil(t, rule)

		// 测试数据：黑名单为空，与空集交集
		testParams := map[string]interface{}{
			"blacklist": []string{},
		}

		paramsJSON, _ := json.Marshal(testParams)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		assert.True(t, result.Passed(), "空集交集规则应该匹配")

		value := result.Aim().AsExpr()
		assert.Equal(t, "allowed", value)
	})

	// 测试用例6: 无交集情况
	t.Run("no_intersect_test", func(t *testing.T) {
		rule, err := ruleService.ruleDAO.GetRule(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, rule)

		// 测试数据：suppliers为[9,10,11]，与规则中的[5,6,7,8]无交集
		testParams := map[string]interface{}{
			"suppliers":   []int{9, 10, 11},
			"supplier_id": 6,
		}

		paramsJSON, _ := json.Marshal(testParams)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)
		assert.NotNil(t, result)

		// 由于supplier_id=6仍然匹配，所以整体规则仍然为true
		assert.True(t, result.Passed(), "即使suppliers无交集，supplier_id匹配仍应返回true")
	})
}

// 演示如何使用规则进行数据过滤
func TestDataFilteringWithIntersect(t *testing.T) {
	ruleService := NewRuleService()
	assert.NotNil(t, ruleService)

	// 模拟酒店数据
	hotels := []map[string]interface{}{
		{
			"id":          1,
			"name":        "北京商务酒店",
			"cities":      []string{"北京", "上海"},
			"hotel_types": []string{"商务酒店", "度假酒店"},
			"amenities":   []string{"WiFi", "健身房"},
			"price":       200,
			"star_rating": 4,
		},
		{
			"id":          2,
			"name":        "上海度假酒店",
			"cities":      []string{"上海", "广州"},
			"hotel_types": []string{"度假酒店"},
			"amenities":   []string{"WiFi", "游泳池"},
			"price":       150,
			"star_rating": 5,
		},
		{
			"id":          3,
			"name":        "深圳精品酒店",
			"cities":      []string{"深圳", "广州"},
			"hotel_types": []string{"精品酒店"},
			"amenities":   []string{"WiFi"},
			"price":       100,
			"star_rating": 3,
		},
	}

	// 使用规则2（城市交集规则）过滤酒店
	rule, err := ruleService.ruleDAO.GetRule(context.Background(), 2)
	assert.NoError(t, err)

	var filteredHotels []map[string]interface{}
	for _, hotel := range hotels {
		paramsJSON, _ := json.Marshal(hotel)
		result, err := rule.RunWithJSON(context.Background(), string(paramsJSON))
		assert.NoError(t, err)

		if result.Passed() {
			filteredHotels = append(filteredHotels, hotel)
		}
	}

	// 验证过滤结果
	assert.Len(t, filteredHotels, 2, "应该有2个酒店通过城市交集规则过滤")

	// 验证第一个酒店（北京商务酒店）
	assert.Equal(t, "北京商务酒店", filteredHotels[0]["name"])

	// 验证第二个酒店（上海度假酒店）
	assert.Equal(t, "上海度假酒店", filteredHotels[1]["name"])
}
