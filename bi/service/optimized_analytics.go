package service

import (
	"context"
	"fmt"
	"time"

	"hotel/bi/domain"
	"hotel/bi/mysql"
	"hotel/bi/protocol"
	"hotel/common/log"
)

// OptimizedAnalyticsService 优化的分析服务
type OptimizedAnalyticsService struct {
	*OrderAnalyticsService
	cache              *domain.AnalyticsCache
	batchProcessor     *domain.BatchProcessor
	performanceMetrics *domain.PerformanceMetrics
}

// NewOptimizedAnalyticsService 创建优化的分析服务
func NewOptimizedAnalyticsService(orderDAO *mysql.OrderAnalyticsDAO) *OptimizedAnalyticsService {
	baseService := NewOrderAnalyticsService(orderDAO)
	cache := domain.NewAnalyticsCache(15 * time.Minute) // 15分钟缓存

	return &OptimizedAnalyticsService{
		OrderAnalyticsService: baseService,
		cache:                 cache,
		performanceMetrics:    &domain.PerformanceMetrics{},
	}
}

// GetOrderAnalyticsWithCache 带缓存的分析数据获取
func (s *OptimizedAnalyticsService) GetOrderAnalyticsWithCache(ctx context.Context, req *protocol.OrderAnalyticsReq) (*protocol.OrderAnalyticsResp, error) {
	startTime := time.Now()
	defer func() {
		s.recordQueryMetrics(time.Since(startTime))
	}()

	// 生成缓存键
	cacheKey := domain.CacheKey{
		Type:      "analytics",
		StartDate: req.StartDate.Format("2006-01-02"),
		EndDate:   req.EndDate.Format("2006-01-02"),
		EntityID:  s.entityIDToString(req.EntityId),
		Extra:     req.Granularity,
	}.String()

	// 尝试从缓存获取
	if cached, exists := s.cache.Get(cacheKey); exists {
		s.recordCacheHit()
		return cached.(*protocol.OrderAnalyticsResp), nil
	}

	s.recordCacheMiss()

	// 从数据库获取
	resp, err := s.OrderAnalyticsService.GetOrderAnalytics(ctx, req)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	s.cache.Set(cacheKey, resp, 10*time.Minute) // 10分钟缓存

	return resp, nil
}

// PrewarmCache 预热缓存
func (s *OptimizedAnalyticsService) PrewarmCache(ctx context.Context) error {
	log.Infoc(ctx, "Starting cache prewarming...")

	now := time.Now()

	// 预热常用的查询
	commonQueries := []*protocol.OrderAnalyticsReq{
		// 今日数据
		{
			StartDate:   &now,
			EndDate:     &now,
			Granularity: "day",
		},
		// 最近7天
		{
			StartDate:   func() *time.Time { t := now.AddDate(0, 0, -7); return &t }(),
			EndDate:     &now,
			Granularity: "day",
		},
		// 最近30天
		{
			StartDate:   func() *time.Time { t := now.AddDate(0, 0, -30); return &t }(),
			EndDate:     &now,
			Granularity: "day",
		},
	}

	for _, req := range commonQueries {
		if _, err := s.GetOrderAnalyticsWithCache(ctx, req); err != nil {
			log.Errorc(ctx, "Failed to prewarm cache for query: %v", err)
		}
	}

	log.Infoc(ctx, "Cache prewarming completed")
	return nil
}

// GetPerformanceMetrics 获取性能指标
func (s *OptimizedAnalyticsService) GetPerformanceMetrics() map[string]interface{} {
	s.performanceMetrics.Mutex.RLock()
	defer s.performanceMetrics.Mutex.RUnlock()

	var cacheHitRate float64
	totalCache := s.performanceMetrics.CacheHitCount + s.performanceMetrics.CacheMissCount
	if totalCache > 0 {
		cacheHitRate = float64(s.performanceMetrics.CacheHitCount) / float64(totalCache) * 100
	}

	return map[string]interface{}{
		"queryCount":     s.performanceMetrics.QueryCount,
		"cacheHitCount":  s.performanceMetrics.CacheHitCount,
		"cacheMissCount": s.performanceMetrics.CacheMissCount,
		"cacheHitRate":   cacheHitRate,
		"avgQueryTime":   s.performanceMetrics.AvgQueryTime,
		"totalQueryTime": s.performanceMetrics.TotalQueryTime,
	}
}

// recordQueryMetrics 记录查询指标
func (s *OptimizedAnalyticsService) recordQueryMetrics(duration time.Duration) {
	s.performanceMetrics.Mutex.Lock()
	defer s.performanceMetrics.Mutex.Unlock()

	s.performanceMetrics.QueryCount++
	s.performanceMetrics.TotalQueryTime += duration
	s.performanceMetrics.AvgQueryTime = s.performanceMetrics.TotalQueryTime / time.Duration(s.performanceMetrics.QueryCount)
}

// recordCacheHit 记录缓存命中
func (s *OptimizedAnalyticsService) recordCacheHit() {
	s.performanceMetrics.Mutex.Lock()
	defer s.performanceMetrics.Mutex.Unlock()
	s.performanceMetrics.CacheHitCount++
}

// recordCacheMiss 记录缓存未命中
func (s *OptimizedAnalyticsService) recordCacheMiss() {
	s.performanceMetrics.Mutex.Lock()
	defer s.performanceMetrics.Mutex.Unlock()
	s.performanceMetrics.CacheMissCount++
}

// entityIDToString 将实体ID转换为字符串
func (s *OptimizedAnalyticsService) entityIDToString(entityId *int64) string {
	if entityId == nil {
		return "nil"
	}
	return fmt.Sprintf("%d", *entityId)
}
