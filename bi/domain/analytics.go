package domain

import (
	"sync"
	"time"
)

// AnalyticsCache 分析数据缓存
type AnalyticsCache struct {
	Data       map[string]interface{}
	Expiry     map[string]time.Time
	Mutex      sync.RWMutex
	DefaultTTL time.Duration
}

// CacheKey 缓存键结构
type CacheKey struct {
	Type      string
	StartDate string
	EndDate   string
	EntityID  string
	Extra     string
}

// String 生成缓存键字符串
func (ck CacheKey) String() string {
	return ck.Type + ":" + ck.StartDate + ":" + ck.EndDate + ":" + ck.EntityID + ":" + ck.Extra
}

// BatchProcessor 批处理器
type BatchProcessor struct {
	Queue     []interface{}
	Mutex     sync.Mutex
	Size      int
	Interval  time.Duration
	Processor func([]interface{}) error
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	QueryCount     int64
	CacheHitCount  int64
	CacheMissCount int64
	AvgQueryTime   time.Duration
	TotalQueryTime time.Duration
	LastResetTime  time.Time
	Mutex          sync.RWMutex
}

// MetricsCollector 实时指标收集器
type MetricsCollector struct {
	TodayOrderCount  int64
	TodayRevenue     float64
	ActiveBookings   int64
	PendingOrders    int64
	CompletionRate   float64
	CancellationRate float64
	LastUpdateTime   time.Time
	Mu               sync.RWMutex
}

// RealTimeMetrics 实时指标
type RealTimeMetrics struct {
	TodayOrders      int64     `json:"todayOrders"`
	TodayRevenue     float64   `json:"todayRevenue"`
	ActiveBookings   int64     `json:"activeBookings"`
	PendingOrders    int64     `json:"pendingOrders"`
	CompletionRate   float64   `json:"completionRate"`
	CancellationRate float64   `json:"cancellationRate"`
	LastUpdateTime   time.Time `json:"lastUpdateTime"`
}

// NewAnalyticsCache 创建分析缓存
func NewAnalyticsCache(defaultTTL time.Duration) *AnalyticsCache {
	cache := &AnalyticsCache{
		Data:       make(map[string]interface{}),
		Expiry:     make(map[string]time.Time),
		DefaultTTL: defaultTTL,
	}

	// 启动清理goroutine
	go cache.StartCleanup()

	return cache
}

// Get 获取缓存数据
func (c *AnalyticsCache) Get(key string) (interface{}, bool) {
	c.Mutex.RLock()
	defer c.Mutex.RUnlock()

	// 检查是否过期
	if expiry, exists := c.Expiry[key]; exists && time.Now().After(expiry) {
		delete(c.Data, key)
		delete(c.Expiry, key)
		return nil, false
	}

	data, exists := c.Data[key]
	return data, exists
}

// Set 设置缓存数据
func (c *AnalyticsCache) Set(key string, value interface{}, ttl ...time.Duration) {
	c.Mutex.Lock()
	defer c.Mutex.Unlock()

	duration := c.DefaultTTL
	if len(ttl) > 0 {
		duration = ttl[0]
	}

	c.Data[key] = value
	c.Expiry[key] = time.Now().Add(duration)
}

// Delete 删除缓存数据
func (c *AnalyticsCache) Delete(key string) {
	c.Mutex.Lock()
	defer c.Mutex.Unlock()

	delete(c.Data, key)
	delete(c.Expiry, key)
}

// Clear 清空所有缓存
func (c *AnalyticsCache) Clear() {
	c.Mutex.Lock()
	defer c.Mutex.Unlock()

	c.Data = make(map[string]interface{})
	c.Expiry = make(map[string]time.Time)
}

// StartCleanup 启动清理过期缓存的goroutine
func (c *AnalyticsCache) StartCleanup() {
	ticker := time.NewTicker(time.Minute * 5) // 每5分钟清理一次
	go func() {
		for range ticker.C {
			c.cleanup()
		}
	}()
}

// cleanup 清理过期缓存
func (c *AnalyticsCache) cleanup() {
	c.Mutex.Lock()
	defer c.Mutex.Unlock()

	now := time.Now()
	expired := make([]string, 0)

	for key, expiry := range c.Expiry {
		if now.After(expiry) {
			expired = append(expired, key)
		}
	}

	for _, key := range expired {
		delete(c.Data, key)
		delete(c.Expiry, key)
	}
}
