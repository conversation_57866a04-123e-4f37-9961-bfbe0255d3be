package domain

import (
	"sync"
	
	"github.com/spf13/cast"
	"os"
	"time"

	commonDomain "hotel/common/domain"
	"hotel/common/types"
)

type Entity struct {
	ID                 types.ID            `json:"id"`
	Type               EntityType          `json:"type"`
	Name               string              `json:"name"`
	RootEntityID       types.ID            `json:"rootEntityId"` // 指向Tenant的根id
	ParentEntityID     types.ID            `json:"parentEntityId"`
	Profile            EntityProfile       `json:"profile"`
	DistributionConfig DistributionConfig  `json:"distributionConfig"` // in
	FinanceConfig      FinanceConfig       `json:"financeConfig"`
	SupplierConfig     SupplierConfig      `json:"supplierConfig"`
	Status             commonDomain.Status `json:"status"`

	CreateTime time.Time `json:"createTime"`
	// ancestor
	Ancestors EntityList `json:"ancestors"` // from root to parent
	
	// 缓存字段（不参与序列化）
	scopeCache      Scope     `json:"-"`
	scopeCacheOnce  sync.Once `json:"-"`
}

func (e *Entity) FindAncestorByLevel(level int) *Entity {
	if e == nil {
		return nil
	}
	return e.GetAncestors().GetIndex(level)
}
func (e *Entity) FormatRoleScope(in string) string {
	if e == nil {
		return ""
	}
	return os.Expand(in, func(s string) string {
		level, exists := RoleScopeVariable2Level[s]
		if !exists {
			return ""
		}
		ancestor := e.FindAncestorByLevel(level)
		if ancestor == nil {
			return ""
		}
		id := ancestor.GetID()
		if id > 0 {
			return cast.ToString(id)
		}
		return ""
	})
}
func (e *Entity) GetTreeList() EntityList {
	return append(e.GetAncestors(), e)
}
func (e *Entity) GetAncestors() EntityList {
	if e == nil {
		return nil
	}
	return e.Ancestors
}
func (e *Entity) GetTenantGroupEntity() *Entity {
	if e == nil {
		return nil
	}
	level, exists := RoleScopeVariable2Level[RoleScopeVariable_TenantGruop]
	if !exists {
		return nil
	}
	return e.GetTreeList().GetIndex(level)
}

func (e *Entity) GetTenantBrandEntity() *Entity {
	if e == nil {
		return nil
	}
	level, exists := RoleScopeVariable2Level[RoleScopeVariable_TenantBrand]
	if !exists {
		return nil
	}
	return e.GetTreeList().GetIndex(level)
}
func (e *Entity) GetCustomerEntity() *Entity {
	if e == nil {
		return nil
	}
	level, exists := RoleScopeVariable2Level[RoleScopeVariable_Customer]
	if !exists {
		return nil
	}
	return e.GetTreeList().GetIndex(level)
}
func (e *Entity) GetScope() Scope {
	if e == nil {
		return ""
	}
	
	// 使用 sync.Once 确保缓存只计算一次
	e.scopeCacheOnce.Do(func() {
		e.scopeCache = NewScopeFromAncestors(e.Ancestors)
	})
	
	return e.scopeCache
}

// ClearScopeCache 清除缓存的 scope
// 当 Entity 的 Ancestors 发生变化时应该调用此方法
func (e *Entity) ClearScopeCache() {
	if e == nil {
		return
	}
	e.scopeCache = ""
	e.scopeCacheOnce = sync.Once{}
}

// UpdateAncestors 更新祖先列表并清除缓存
func (e *Entity) UpdateAncestors(ancestors EntityList) {
	if e == nil {
		return
	}
	e.Ancestors = ancestors
	e.ClearScopeCache()
}

func (e *Entity) GetID() types.ID {
	if e == nil {
		return 0
	}
	return e.ID
}
func (e *Entity) GetIDStr() string {
	if e == nil {
		return ""
	}
	return cast.ToString(e.ID)
}
func (e *Entity) GetName() string {
	if e == nil {
		return ""
	}
	return e.Name
}
func (e *Entity) GetParentEntityID() types.ID {
	if e == nil {
		return 0
	}
	return e.ParentEntityID
}
func (e *Entity) GetRootEntityID() types.ID {
	if e == nil {
		return 0
	}
	return e.RootEntityID
}
func (e *Entity) IsPlatform() bool {
	return e != nil && e.Type.HasType(EntityTypePlatform)
}
func (e *Entity) IsTenant() bool {
	return e != nil && e.Type.HasType(EntityTypeTenant)
}
func (e *Entity) IsSupplier() bool {
	return e != nil && e.Type.HasType(EntityTypeSupplier)
}
func (e *Entity) IsTenantRoot() bool {
	return e != nil && e.Type.HasType(EntityTypeTenant) && (e.GetID() == e.GetTenantGroupEntity().GetID())
}
func (e *Entity) IsTenantBrand() bool {
	return e != nil && e.Type.HasType(EntityTypeTenant) && (e.GetID() == e.GetTenantBrandEntity().GetID())
}
func (e *Entity) IsCustomer() bool {
	return e != nil && e.Type.HasType(EntityTypeCustomer)
}
func (e *Entity) IsExtranet() bool {
	return e != nil && e.Type.HasType(EntityTypeExtranet)
}

// HasType 检查实体是否包含指定类型
func (e *Entity) HasType(t EntityType) bool {
	if e == nil {
		return false
	}
	return e.Type.HasType(t)
}

// AddType 为实体添加类型
func (e *Entity) AddType(t EntityType) {
	if e != nil {
		e.Type = e.Type.AddType(t)
	}
}

// RemoveType 为实体移除类型
func (e *Entity) RemoveType(t EntityType) {
	if e != nil {
		e.Type = e.Type.RemoveType(t)
	}
}

// GetTypeString 获取类型的字符串表示
func (e *Entity) GetTypeString() string {
	if e == nil {
		return "Unknown"
	}
	return e.Type.String()
}

var (
	_platformEntity = &Entity{
		Type:   EntityTypePlatform,
		Status: commonDomain.StatusEnable,
	}
)

func GetPlatformEntity() *Entity {
	return _platformEntity
}

type EntityList []*Entity

func (l EntityList) IDs() (out types.IDs) {
	for i := 0; i < len(l); i++ {
		out = append(out, l[i].ID)
	}
	return out
}

func (l EntityList) Int64IDs() (out []int64) {
	for i := 0; i < len(l); i++ {
		out = append(out, l[i].ID.Int64())
	}
	return out
}

func (l EntityList) Reverse() EntityList {
	reversed := make(EntityList, len(l))
	for i := 0; i < len(l); i++ {
		reversed[i] = l[len(l)-1-i]
	}
	return reversed
}
func (l EntityList) GetIndex(i int) *Entity {
	if i < 0 || i >= len(l) {
		return nil
	}
	return l[i]
}
func (l EntityList) Filter(f func(entity *Entity) bool) (out EntityList) {
	for _, r := range l {
		if f(r) {
			out = append(out, r)
		}
	}
	return out
}
func (l EntityList) FindByID(id types.ID) *Entity {
	ll := l.Filter(func(entity *Entity) bool {
		return entity.ID == id
	})
	if len(ll) == 0 {
		return nil
	}
	return ll[0]
}
