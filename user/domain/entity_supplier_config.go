package domain

import (
	"github.com/bytedance/sonic"

	"hotel/supplier/domain"
)

type SupplierConfig struct {
	Email       *SupplierEmailConfig `json:"email"`
	Credentials []*Credential        `json:"credentials"`
}

type Credential struct {
	Supplier          domain.Supplier `json:"supplier"`
	CredentialId      int64           `json:"credentialId"`
	CredentialContent string          `json:"credentialContent"`
}

func (s SupplierConfig) IsNil() bool {
	return false
}

type SupplierEmailConfig struct {
	Domain         string            `json:"domain"`
	From           map[string]string `json:"from"`            // market -> <EMAIL>
	SupplierParams map[string]string `json:"supplierParams"` // "aws-ses" -> "{}"
}

func NewSupplierConfigFromString(s string) (out SupplierConfig) {
	if err := sonic.UnmarshalString(s, &out); err != nil {
		return SupplierConfig{}
	}
	return out
}
