package domain

import (
	"github.com/Danceiny/sentinel-golang/core/flow"
	"time"

	"hotel/common/types"
)

type LinkStatus = int

const (
	LinkStatusPending LinkStatus = iota
	LinkStatusRequested
	LinkStatusConnected
	LinkStatusDeactivated
)

type EntityUserLink struct {
	EntityId             types.ID           `json:"entityId"` // 所属实体id
	UserId               types.ID           `json:"userId"`   // 用户 ID
	Status               LinkStatus         `json:"status"`
	SellerInNodeRuleId   int64              `json:"sellerInNodeRuleId"`   // 卖家入节点的规则id
	SellerOutNodeRuleId  int64              `json:"sellerOutNodeRuleId"`  // 卖家在节点的规则id
	BuyerInNodeRuleId    int64              `json:"buyerInNodeRuleId"`    // 买家入节点的规则id
	BuyerOutNodeRuleId   int64              `json:"buyerOutNodeRuleId"`   // 买家出节点的规则id
	SellerInCredentialId int64              `json:"sellerInCredentialId"` // 卖家入节点的凭证id
	RateLimit            *flow.Rule         `json:"rateLimit"`            // 限流规则
	Info                 EntityUserLinkInfo `json:"info"`                 // 其他信息
	CreateTime           time.Time          `json:"createTime"`
}

type EntityUserLinkInfo struct {
}
