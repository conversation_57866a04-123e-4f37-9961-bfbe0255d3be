package service

import (
	"context"
	"hotel/common/envhelper"
	"hotel/common/quota"
	ruleSrv "hotel/rule/service"
	"sync"
	"time"

	"github.com/Danceiny/sentinel-golang/core/flow"
	pkgerr "github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	configutils "hotel/common/config"
	"hotel/common/fanout"
	"hotel/common/log"
	"hotel/notify/email"
	"hotel/user/config"
	"hotel/user/domain"
	"hotel/user/mysql"
)

type UserService struct {
	c               *config.Config
	dao             *mysql.Dao
	mailFact        *email.MailServiceFactory
	fan             *fanout.Fanout
	ruleSrv         *ruleSrv.RuleService
	quotaDatasource *quota.Datasource
}

var (
	_userSrv *UserService
	_once    sync.Once
)

func (s *UserService) Name() string {
	return "user"
}

var configFile = configutils.SafeFlagString("user", "user/config/config.yaml", "user config file")

func NewUserService() *UserService {
	_once.Do(func() {
		var cfg config.Config
		conf.MustLoad(*configFile, &cfg)
		userDB := sqlx.NewMysql(cfg.MySQL.User)
		redisCli, err := redis.NewRedis(cfg.Redis)
		if err != nil {
			panic(err)
		}
		_userSrv = &UserService{
			dao:      mysql.NewDao(userDB, redisCli),
			mailFact: email.NewFactory(),
			ruleSrv:  ruleSrv.NewRuleService(),
			fan:      fanout.New("UserService"),
			quotaDatasource: &quota.Datasource{
				Channel: make(chan *flow.Rule, 100), // 增加缓冲区大小以避免阻塞
			},
		}
		if err := _userSrv.fan.Do(context.Background(), func(ctx context.Context) {
			if err := _userSrv.initCheck(); err != nil {
				panic(err)
			}
		}); err != nil {
			panic(err)
		}
	})

	return _userSrv
}

func (s *UserService) initCheck() error {
	if envhelper.IsDev() {
		// 异步发送 nil 标记，避免阻塞
		go func() {
			select {
			case s.quotaDatasource.Channel <- nil:
			default:
				log.Error("Failed to send nil marker to quota channel")
			}
		}()
		return nil
	}

	st := time.Now()
	ctx := context.Background()

	for i, fun := range []func(ctx context.Context) error{
		s.createDemoRoles,
		s.createDemoEntities,
		s.createDemoUsers,
		s.createDemoCredentials,
	} {
		if err := fun(ctx); err != nil {
			log.Error("initCheck idx(%v) err:%v", i, err)
			return err
		}
	}

	entityUserLinks, err := s.dao.User.ListAllEntityUserLinks(ctx)
	if err != nil {
		return err
	}
	
	// 异步发送规则，避免阻塞主线程
	go func() {
		for _, entityUserLink := range entityUserLinks {
			select {
			case s.quotaDatasource.Channel <- entityUserLink.RateLimit:
				log.Info("Successfully sent rate limit rule to quota channel")
			default:
				log.Error("Failed to send rate limit rule to quota channel, channel may be full")
			}
		}
	}()

	log.Info("initCheck end, costTime:%s", time.Since(st))
	return nil
}

func (s *UserService) createDemoRoles(ctx context.Context) error {
	for _, v := range domain.PredefinedRoleList {
		if err := s.dao.Role.UpsertRole(ctx, v); err != nil {
			return pkgerr.Wrapf(err, "UpsertRole(%#v)", v)
		}
	}
	return nil
}

func (s *UserService) createDemoEntities(ctx context.Context) error {
	for _, v := range domain.PredefinedEntityList {
		if err := s.dao.Entity.UpsertEntity(ctx, v); err != nil {
			return pkgerr.Wrapf(err, "UpdateEntity(%#v)", v)
		}
	}
	return nil
}
func (s *UserService) createDemoCredentials(ctx context.Context) error {
	for _, v := range domain.PredefinedCredentialList {
		if err := s.dao.SupplierCredential.Upsert(ctx, v); err != nil {
			return pkgerr.Wrapf(err, "createDemoCredential upsert(%#v)", v)
		}
	}
	return nil
}
func (s *UserService) createDemoUsers(ctx context.Context) error {
	for _, v := range domain.PredefinedUsers {
		if err := v.UserBasic.EncryptPassword(); err != nil {
			return pkgerr.Wrapf(err, "EncryptPassword(%v)", v)
		}
		if err := s.dao.Transact(ctx, func(ctx context.Context, tx sqlx.Session) error {
			if err := s.dao.User.TxUpsertUser(ctx, tx, v.UserBasic); err != nil {
				return pkgerr.Wrapf(err, "UpsertUser(%#v)", v)
			}
			for _, e := range v.UserEntityConnections {
				for _, r := range e.Roles {
					if err := s.dao.User.TxAssignUserRole(ctx, tx, v.ID, e.Entity.ID, r); err != nil {
						return pkgerr.Wrapf(err, "AssignUserRole(%#v, %#v, %#v)", v, e.Entity, r)
					}
				}
			}
			return nil
		}); err != nil {
			return err
		}
	}
	return nil
}

func (s *UserService) GetQuotaDatasource() *quota.Datasource {
	return s.quotaDatasource
}
