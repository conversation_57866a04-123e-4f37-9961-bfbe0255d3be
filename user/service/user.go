package service

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/idgen"
	"hotel/common/log"
	"hotel/common/types"

	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"hotel/common/bff"
	"hotel/common/bizerr"
	commonDomain "hotel/common/domain"
	"hotel/common/i18n"
	"hotel/common/pagehelper"
	"hotel/common/utils"
	"hotel/user/domain"
	"hotel/user/mysql"
	"hotel/user/protocol"
)

// ListUser
// @desc: 展示用户列表
// @path: /listUser
// @tags: internal.hotelbyte.com
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
// @response: 403,PermissionDeniedErr,"无权限"
// @response: 12345,TestErr,"测试"
func (s *PlatformService) ListUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	// 只返回platform类型的用户
	platformType := domain.EntityTypePlatform
	return s.UserService.ListUserWithTypeFilter(ctx, req, &platformType)
}

// ListEntity
// @desc: 展示实体列表
// @path: /listEntity
// @tags: internal.hotelbyte.com
// @auth: required
// @response: 404,EntityNotFound,"实体不存在"
func (s *PlatformService) ListEntity(ctx context.Context, req *protocol.ListEntityReq) (*protocol.ListEntityResp, error) {
	return s.UserService.ListEntity(ctx, req)
}

// ListUser
// @desc: 展示用户列表
// @path: /listUser
// @tags: admin.hotelbyte.com/tenant
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
func (s *TenantService) ListUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	// todo: check req.EntityIDs
	// 只返回tenant类型的用户
	tenantType := domain.EntityTypeTenant
	return s.UserService.ListUserWithTypeFilter(ctx, req, &tenantType)
}

// ListUser
// @desc: 展示用户列表
// @path: /listUser
// @tags: admin.hotelbyte.com/customer
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404 UserNotFound "用户不存在"
func (s *CustomerService) ListUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	// 只返回customer类型的用户
	customerType := domain.EntityTypeCustomer
	return s.UserService.ListUserWithTypeFilter(ctx, req, &customerType)
}

// ListUser
// @desc: 展示用户列表
// @path: /listUser
// @auth: required
// @tags: internal.hotelbyte.com/extranet
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404 UserNotFound "用户不存在"
func (s *ExtranetService) ListUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	// 只返回extranet类型的用户
	extranetType := domain.EntityTypeExtranet
	return s.UserService.ListUserWithTypeFilter(ctx, req, &extranetType)
}

// DownloadUser
// @desc: 展示用户列表
// @path: /downloadUser
// @tags: platform
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
// @response: 403,PermissionDeniedErr,"无权限"
// @response: 12345,TestErr,"测试"
func (s *PlatformService) DownloadUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	return s.UserService.DownloadUser(ctx, req)
}

// DownloadUser
// @desc: 展示用户列表
// @path: /downloadUser
// @tags: tenant
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
// @response: 403,PermissionDeniedErr,"无权限"
// @response: 12345,TestErr,"测试"
func (s *TenantService) DownloadUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	return s.UserService.DownloadUser(ctx, req)
}

// DownloadUser
// @desc: 展示用户列表
// @path: /downloadUser
// @tags: customer
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
// @response: 403,PermissionDeniedErr,"无权限"
// @response: 12345,TestErr,"测试"
func (s *CustomerService) DownloadUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	return s.UserService.DownloadUser(ctx, req)
}

// DownloadUser
// @desc: 展示用户列表
// @path: /downloadUser
// @tags: extranet
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
// @response: 403,PermissionDeniedErr,"无权限"
// @response: 12345,TestErr,"测试"
func (s *ExtranetService) DownloadUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	return s.UserService.DownloadUser(ctx, req)
}

// ListUser
// @path: /internal/listUser
// @tags: internal.hotelbyte.com
func (s *UserService) ListUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	return s.ListUserWithTypeFilter(ctx, req, nil)
}

// ListUserWithTypeFilter 支持用户类型过滤的用户列表查询
func (s *UserService) ListUserWithTypeFilter(ctx context.Context, req *protocol.ListUserReq, entityTypeFilter *domain.EntityType) (*protocol.ListUserResp, error) {
	// 全量 db 加载
	out, err := s.dao.User.GetUserByEntityIDsAndStatus(ctx, req.EntityIDs, req.Activated)
	if err != nil {
		return nil, err
	}
	logx.WithContext(ctx).Infof("out:%s", utils.ToJSON(out))

	// 如果指定了实体类型过滤，则进行过滤
	if entityTypeFilter != nil {
		out = s.filterUsersByEntityType(out, *entityTypeFilter)
	}

	var ffUsers []*bff.ElementRow[domain.User]

	// 安全的分页处理，避免空数组切片越界和nil指针错误
	var start, end int
	if req.Page != nil {
		start = int(req.Page.GetOffset())
		end = int(req.Page.GetNextPageOffset())
	} else {
		// 如果没有分页参数，返回所有数据
		start = 0
		end = len(out)
	}

	// 确保索引不超出数组范围
	if start > len(out) {
		start = len(out)
	}
	if end > len(out) {
		end = len(out)
	}

	for _, user := range out[start:end] {
		// 构建用户状态显示
		statusText := i18n.I18N{Zh: "未激活", En: "Inactive", Ar: "غير نشط"}
		if user.Activated {
			statusText = i18n.I18N{Zh: "已激活", En: "Active", Ar: "نشط"}
		}

		// 构建角色信息
		var roleNames []string
		for _, conn := range user.UserEntityConnections {
			for _, role := range conn.Roles {
				roleNames = append(roleNames, role.Name)
			}
		}
		rolesTextStr := "无角色"
		if len(roleNames) > 0 {
			rolesTextStr = strings.Join(roleNames, ", ")
		}
		rolesText := i18n.I18N{En: rolesTextStr, Zh: rolesTextStr, Ar: rolesTextStr}

		// 构建实体信息
		var entityNames []string
		for _, conn := range user.UserEntityConnections {
			if conn.Entity != nil {
				entityNames = append(entityNames, conn.Entity.Name)
			}
		}
		entitiesTextStr := "无实体"
		if len(entityNames) > 0 {
			entitiesTextStr = strings.Join(entityNames, ", ")
		}
		entitiesText := i18n.I18N{En: entitiesTextStr, Zh: entitiesTextStr, Ar: entitiesTextStr}

		// 构建操作按钮
		actionButtons := []bff.Button{
			{
				ID: bff.ActionIdEdit,
				Content: bff.ElementItem{
					Type: bff.ElementTypeText,
					Content: &bff.ElementText{
						Text: i18n.I18N{
							Zh: "编辑",
							En: "Edit",
							Ar: "تحرير",
						},
					},
				},
				Hover: i18n.I18N{
					Zh: "编辑用户",
					En: "Edit User",
					Ar: "تحرير المستخدم",
				},
				JumpURL: fmt.Sprintf("/user/edit/%d", user.ID),
			},
			{
				ID: bff.ActionIdDelete,
				Content: bff.ElementItem{
					Type: bff.ElementTypeText,
					Content: &bff.ElementText{
						Text: i18n.I18N{
							Zh: "删除",
							En: "Delete",
							Ar: "حذف",
						},
					},
				},
				Hover: i18n.I18N{
					Zh: "删除用户",
					En: "Delete User",
					Ar: "حذف المستخدم",
				},
			},
		}

		ffUsers = append(ffUsers, &bff.ElementRow[domain.User]{
			Raw: *user,
			Key: user.Key,
			Columns: []bff.ElementItem{
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: fmt.Sprintf("%d", user.ID), Zh: fmt.Sprintf("%d", user.ID), Ar: fmt.Sprintf("%d", user.ID)},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: user.Username, Zh: user.Username, Ar: user.Username},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: user.Key, Zh: user.Key, Ar: user.Key},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: statusText,
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: rolesText,
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: entitiesText,
					},
				},
				{
					Type: bff.ElementTypeTime,
					Content: &bff.ElementTime{
						Time: user.CreateTime,
					},
				},
				{
					Type: bff.ElementTypeButtonGroup,
					Content: bff.ButtonGroupContent{
						Buttons: actionButtons,
					},
				},
			},
		})
	}

	// 构建多语言表头
	headers := []bff.ElementItem{
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "ID",
					En: "ID",
					Ar: "المعرف",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "用户名",
					En: "Username",
					Ar: "اسم المستخدم",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "邮箱/Key",
					En: "Email/Key",
					Ar: "البريد الإلكتروني/المفتاح",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "状态",
					En: "Status",
					Ar: "الحالة",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "角色",
					En: "Role",
					Ar: "الدور",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "实体",
					En: "Entity",
					Ar: "الكيان",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "创建时间",
					En: "Created Time",
					Ar: "وقت الإنشاء",
				},
			},
		},
		{
			Type: bff.ElementTypeText,
			Content: bff.TextContent{
				Text: i18n.I18N{
					Zh: "操作",
					En: "Actions",
					Ar: "الإجراءات",
				},
			},
		},
	}

	return &protocol.ListUserResp{
		bff.Table[domain.User]{
			HeaderKeys: []string{"ID", "用户名", "邮箱/Key", "状态", "角色", "实体", "创建时间", "操作"},
			Header:     headers,
			Rows:       ffUsers,
			PageResp:   pagehelper.PageResp{Total: int64(len(out))},
		}}, nil
}

// UpsertPrivilege
// @path: /internal/upsertPrivilege
// @tags: internal.hotelbyte.com
func (s *UserService) UpsertPrivilege(ctx context.Context, req *protocol.CreatePrivilegeReq) error {
	return s.dao.Role.UpsertPrivilege(ctx, req.Privilege)
}

// UpsertRole
// @path: /internal/upsertRole
// @tags: internal.hotelbyte.com
func (s *UserService) UpsertRole(ctx context.Context, req *protocol.CreateRoleReq) error {
	return s.dao.Role.UpsertRole(ctx, req.Role)
}

// Register
// @path: /internal/register
// @tags: internal.hotelbyte.com
func (s *UserService) Register(ctx context.Context, req *protocol.RegisterUserReq) (*protocol.RegisterUserResp, error) {
	link, err := url.Parse(req.Link)
	if err != nil {
		return nil, err
	}
	eid := types.ID(cast.ToInt64(link.Query().Get("entityId")))
	rname := link.Query().Get("role")
	entity, err := s.dao.Entity.Get(ctx, eid)
	if err != nil {
		return nil, err
	}
	if entity == nil {
		log.Error("Register entity.Get is null, req： %v", req)
	}
	role, err := s.dao.Role.GetRoleByName(ctx, rname)
	if err != nil {
		return nil, err
	}

	if role == nil {
		log.Error("Register role.Get is null, req： %v", req)
	}

	uid := req.User.GetID()
	user, err := s.dao.User.GetUser(ctx, uid)
	if err != nil {
		return nil, err
	}
	if err := s.dao.Transact(ctx, func(ctx context.Context, tx sqlx.Session) error {
		var userID = idgen.MustNextInt64ID()

		if err = s.dao.User.TxCreateUserBasic(ctx, tx, user.UserBasic); err != nil {
			return err
		}

		entityID := eid
		// 分配指定角色
		for _, role := range user.UserEntityConnections[eid].Roles {
			if role.Name == rname {
				role.Status = commonDomain.StatusEnable
				if err = s.dao.User.TxAssignUserRole(ctx, tx, userID, entityID, role); err != nil {
					return err
				}
			}
		}
		return err
	}); err != nil {
		return nil, err
	}

	return &protocol.RegisterUserResp{}, nil
}

// GetUser
// @desc: platform用户详情页
// @path: /getUser
// @tags: internal.hotelbyte.com
// @auth: required
func (s *PlatformService) GetUser(ctx context.Context, req *protocol.GetUserReq) (*protocol.GetUserResp, error) {
	return s.UserService.GetUser(ctx, req)
}

// GetUser
// @desc: tenant用户详情页
// @path: /getUser
// @tags: admin.hotelbyte.com/tenant
// @auth: required
func (s *TenantService) GetUser(ctx context.Context, req *protocol.GetUserReq) (*protocol.GetUserResp, error) {
	return s.UserService.GetUser(ctx, req)
}

// GetUser
// @desc: customer用户详情页
// @path: /getUser
// @tags: admin.hotelbyte.com/customer
func (s *CustomerService) GetUser(ctx context.Context, req *protocol.GetUserReq) (*protocol.GetUserResp, error) {
	return s.UserService.GetUser(ctx, req)
}

// GetUser
// @desc: extranet用户详情页
// @path: /getUser
// @tags: admin.hotelbyte.com/extranet
func (s *ExtranetService) GetUser(ctx context.Context, req *protocol.GetUserReq) (*protocol.GetUserResp, error) {
	return s.UserService.GetUser(ctx, req)
}

// GetUser
// @apidoc: -
// @tags: internal.hotelbyte.com
func (s *UserService) GetUser(ctx context.Context, req *protocol.GetUserReq) (*protocol.GetUserResp, error) {
	user, err := s.dao.User.GetUserByKey(ctx, req.Key)
	if errors.Is(err, mysql.ErrNotFound) {
		return nil, bizerr.NotFoundErr
	}
	if err != nil {
		return nil, err
	}

	return &protocol.GetUserResp{User: user}, nil
}

func (s *UserService) DownloadUser(ctx context.Context, req *protocol.ListUserReq) (*protocol.ListUserResp, error) {
	return &protocol.ListUserResp{}, nil
}

// filterUsersByEntityType 根据实体类型过滤用户
func (s *UserService) filterUsersByEntityType(users []*domain.User, entityType domain.EntityType) []*domain.User {
	var filteredUsers []*domain.User
	for _, user := range users {
		// 检查用户是否有指定类型的实体连接
		hasTargetType := false
		for _, conn := range user.UserEntityConnections {
			if conn.Entity != nil && conn.Entity.Type.HasType(entityType) {
				hasTargetType = true
				break
			}
		}
		if hasTargetType {
			filteredUsers = append(filteredUsers, user)
		}
	}
	return filteredUsers
}
