package protocol

import (
	"hotel/common/bff"
	"hotel/user/domain"
)

type // ReSendInvitationReq represents the request structure for ReSendInvitation operation with 2 fields
ReSendInvitationReq struct {
	// Operator contains the operator data
	Operator *domain.User `json:"operator" apidoc:"-"` // 操作人
	// Target contains the target data
	Target *domain.UserBasic `json:"target"` // 被邀请人
}
type // ReSendInvitationResp represents the response structure for ReSendInvitation operation
ReSendInvitationResp struct{}
type // BatchInviteUserReq represents the request structure for BatchInviteUser operation with 4 fields
BatchInviteUserReq struct {
	// Operator contains the operator data
	Operator *domain.User `json:"operator" apidoc:"-"` // 操作人
	// Targets contains the targets data
	Targets []*BatchInviteUserItem `json:"targets"` // 被邀请人
	// InviteType contains the invitetype data
	InviteType domain.PrivilegeCode `json:"inviteType"` // 邀请类型
	// ParentEntity contains the parententity data
	ParentEntity *domain.Entity `json:"parentEntity"`
}
type // BatchInviteUserResp represents the response structure for BatchInviteUser operation with 5 fields
BatchInviteUserResp struct {
	// Results contains the results data
	Results []*RowOperateResult `json:"results,omitzero"`
	// SuccessCount represents the count of Success
	SuccessCount int `json:"successCount"`
	// FailureCount represents the count of Failure
	FailureCount int `json:"failureCount"`
	// FailureMsg contains the failuremsg value
	FailureMsg string `json:"failureMsg"`
	// TraceId is the unique identifier for this Trace
	TraceId string `json:"traceId"` // 可以直接代入粘贴板，提示用户带上这个 id 来报告问题
}

type // RowOperateResult represents a data structure for API communication with 1 fields
RowOperateResult struct {
	// Success indicates whether this  was successful
	Success bool `json:"success,omitempty"`
}

type // BatchInviteUserItem represents a data structure for API communication with 2 fields
BatchInviteUserItem struct {
	*domain.UserBasic
	// Roles contains the roles data
	Roles []*domain.UserRole `json:"roles"` // 授予被邀请人的角色
}

type // InviteUserReq represents the request structure for InviteUser operation with 5 fields
InviteUserReq struct {
	Operator *domain.User `json:"operator" apidoc:"-"` // 操作人
	// Target contains the target data
	Target *domain.UserBasic `json:"target"` // 被邀请人
	// 被邀请人所属的实体,是从上一步 getInvitationInfo 接口返回中拿到的
	// 邀请 tenant 用户时，parent 就是 tenant 的 root 或者 brand
	// 邀请 customer 的 root 用户时，parent 就是 brand (此时会同步创建 customer 的实体）；以用户名创建entity
	// 邀请 customer 的非 root用户时，parent 就是 customer 的 root 非root 的customer不考虑，后续支持的话，指的就是他的parent是root customer而不是brand或者tenant
	ParentEntity *domain.Entity `json:"parentEntity"`
	// InviteType contains the invitetype data
	InviteType domain.PrivilegeCode `json:"inviteType"` // 邀请类型
	Roles      []*domain.UserRole   `json:"roles"`      // 授予被邀请人的角色
}
type // InviteUserResp represents the response structure for InviteUser operation
InviteUserResp struct {
}
type // GetInvitationInfoReq represents the request structure for GetInvitationInfo operation with 4 fields
GetInvitationInfoReq struct {
	// Operator contains the operator data
	Operator *domain.User `apidoc:"-"`
	// BrandEntityId is the unique identifier for this BrandEntity
	BrandEntityId int64 `json:"brandEntityId,omitempty"` // 用于限制邀请信息范围以及默认填充
	// CustomerEntityId is the unique identifier for this CustomerEntity
	CustomerEntityId int64 `json:"customerEntityId,omitempty"` // 用于限制邀请信息范围以及默认填充
	// InviteType contains the invitetype data
	InviteType domain.PrivilegeCode `json:"inviteType"` // 用于过滤邀请信息
}
type // GetInvitationInfoResp represents the response structure for GetInvitationInfo operation with 3 fields
GetInvitationInfoResp struct {
	// AvailableRoles contains the availableroles data
	AvailableRoles []*EntityInvitationInfo `json:"availableRoles"` // 内部后台专用
	// UserDetails contains detailed information about this UserDetails
	UserDetails bff.Table[InvitationInfoUserDetail] `json:"userDetails"` // BFF表格，一列就是一个用户
	// UserRoles contains the userroles data
	UserRoles bff.Table[domain.UserRole] `json:"userRoles"` // BFF表格，一行就是一个角色
}

type // InvitationInfoUserDetail represents a data structure for API communication with 3 fields
InvitationInfoUserDetail struct {
	// Email is the email address for this
	Email string `json:"email"`
	// AvailableBrandEntities contains the availablebrandentities data
	AvailableBrandEntities []*domain.Entity `json:"availableBrandEntities"` // 品牌下拉筛选
	// CustomerEntity contains the customerentity data
	CustomerEntity *domain.Entity `json:"customerEntity"`
}

type // EntityInvitationInfo represents a data structure for API communication with 2 fields
EntityInvitationInfo struct {
	// ParentEntity contains the parententity data
	ParentEntity *domain.Entity `json:"parentEntity"`
	// UserRole2Entities contains the userrole2entities data
	UserRole2Entities []*UserRoleToEntities `json:"userRole2Entities"` //
}
type // UserRoleToEntities represents a data structure for API communication with 2 fields
UserRoleToEntities struct {
	// UserRole contains the userrole data
	UserRole *domain.UserRole `json:"userRole"`
	// Entities contains the entities data
	Entities []*domain.Entity `json:"entities"`
}

type // VerifyLinkReq represents the request structure for VerifyLink operation with 1 fields
VerifyLinkReq struct {
	// Link contains the link value
	Link string `json:"link"`
}

type // VerifyLinkResp represents the response structure for VerifyLink operation
VerifyLinkResp struct {
}
