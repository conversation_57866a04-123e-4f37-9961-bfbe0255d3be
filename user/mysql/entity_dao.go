package mysql

import (
	"context"

	"github.com/pkg/errors"

	commonDomain "hotel/common/domain"
	"hotel/common/types"
	"hotel/common/utils"
	"hotel/user/domain"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type EntityDao struct {
	entity *EntityModel
}

func newEntityDao(entity *EntityModel) *EntityDao {
	return &EntityDao{entity: entity}
}

func (d *EntityDao) TxCreateEntity(ctx context.Context, tx sqlx.Session, entity *domain.Entity) error {
	_, err := d.entity.withSession(tx).Insert(ctx, convertModelEntity(entity))
	if err != nil {
		return err
	}
	return nil
}

func (d *EntityDao) CreateEntity(ctx context.Context, entity *domain.Entity) error {
	_, err := d.entity.Insert(ctx, convertModelEntity(entity))
	if err != nil {
		return err
	}
	return nil
}

func (d *EntityDao) UpdateEntity(ctx context.Context, entity *domain.Entity) error {
	return d.entity.Update(ctx, convertModelEntity(entity))
}
func (d *EntityDao) UpsertEntity(ctx context.Context, entity *domain.Entity) error {
	v, err := d.entity.FindOne(ctx, entity.ID.Int64())
	if err != nil && !errors.Is(err, ErrNotFound) {
		return err
	}
	if v == nil {
		_, err := d.entity.Insert(ctx, convertModelEntity(entity))
		if err != nil {
			return err
		}
		return err
	}
	return d.entity.Update(ctx, convertModelEntity(entity))
}
func (d *EntityDao) FindByRootID(ctx context.Context, rootID int64) ([]*domain.Entity, error) {
	res, err := d.entity.FindByRootId(ctx, rootID)
	if err != nil {
		return nil, err
	}
	return convertDomainEntityList(res), nil
}
func (d *EntityDao) FindByRootIDs(ctx context.Context, rootIDs []int64) ([]*domain.Entity, error) {
	res, err := d.entity.FindByRootIds(ctx, rootIDs)
	if err != nil {
		return nil, err
	}
	return convertDomainEntityList(res), nil
}

func (d *EntityDao) FindAll(ctx context.Context) ([]*domain.Entity, error) {
	res, err := d.entity.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	return convertDomainEntityList(res), nil
}

// todo: add cache
func (d *EntityDao) Get(ctx context.Context, id types.ID) (*domain.Entity, error) {
	res, err := d.entity.FindOne(ctx, id.Int64())
	if err != nil {
		return nil, err
	}
	return convertDomainEntityBase(res), nil
}

func (d *EntityDao) TraverseByLeafID(ctx context.Context, rootID int64) ([]*domain.Entity, error) {
	res, err := d.entity.FindByRootId(ctx, rootID)
	if err != nil {
		return nil, err
	}
	return convertDomainEntityList(res), nil
}

// FindByType 根据实体类型查询实体（支持位运算）
func (d *EntityDao) FindByType(ctx context.Context, entityType domain.EntityType) ([]*domain.Entity, error) {
	res, err := d.entity.FindByType(ctx, uint64(entityType))
	if err != nil {
		return nil, err
	}
	return convertDomainEntityList(res), nil
}

// FindByTypeAny 查询包含任意指定类型的实体
func (d *EntityDao) FindByTypeAny(ctx context.Context, entityType domain.EntityType) ([]*domain.Entity, error) {
	res, err := d.entity.FindByTypeAny(ctx, uint64(entityType))
	if err != nil {
		return nil, err
	}
	return convertDomainEntityList(res), nil
}

func convertModelEntity(in *domain.Entity) *Entity {
	return &Entity{
		Id:                 in.ID.Int64(),
		Type:               uint64(in.Type), // 更新为uint64以支持位运算
		Name:               in.Name,
		RootEntityId:       in.RootEntityID.Int64(),
		ParentEntityId:     in.ParentEntityID.Int64(),
		Profile:            utils.ToJSON(in.Profile),
		DistributionConfig: utils.ToJSON(in.DistributionConfig),
		FinanceConfig:      utils.ToJSON(in.FinanceConfig),
		SupplierConfig:     utils.ToJSON(in.SupplierConfig),
		Status:             uint64(in.Status),
		CreateTime:         in.CreateTime,
	}
}

func convertDomainEntityList(ancestor []*Entity) (out []*domain.Entity) {
	m := convertDomainEntityMap(ancestor)
	for _, v := range ancestor {
		out = append(out, m[types.ID(v.Id)])
	}
	return out
}

func convertDomainEntityMap(ancestor []*Entity) map[types.ID]*domain.Entity {
	m := map[types.ID]*domain.Entity{}
	for _, v := range ancestor {
		m[types.ID(v.Id)] = convertDomainEntityBase(v)
	}
	for _, v := range m {
		pid := v.ParentEntityID
		for pid > 0 {
			if m[pid] == nil {
				break // dirty data
			}
			v.Ancestors = append(v.Ancestors, m[pid])
			pid = m[pid].ParentEntityID
		}
		v.Ancestors = v.Ancestors.Reverse()
	}
	return m
}

func convertDomainEntityBase(in *Entity) *domain.Entity {
	return &domain.Entity{
		ID:                 types.ID(in.Id),
		Type:               domain.EntityType(in.Type), // 从uint64转换为EntityType
		Name:               in.Name,
		RootEntityID:       types.ID(in.RootEntityId),
		ParentEntityID:     types.ID(in.ParentEntityId),
		Profile:            domain.NewEntityProfileFromString(in.Profile),
		DistributionConfig: domain.NewDistributionConfigFromString(in.DistributionConfig),
		FinanceConfig:      domain.NewFinanceConfigFromString(in.FinanceConfig),
		SupplierConfig:     domain.NewSupplierConfigFromString(in.SupplierConfig),
		Status:             commonDomain.Status(in.Status),
	}
}
func convertDomainEntity(in *Entity, ancestor []*Entity) *domain.Entity {
	out := convertDomainEntityMap(append(ancestor, in))
	return out[types.ID(in.Id)]
}
