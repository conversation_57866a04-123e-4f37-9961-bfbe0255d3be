package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// QuestionnaireDAO 问卷数据访问层
type QuestionnaireDAO struct {
	questionnaireModel *QuestionnaireModel
}

// NewQuestionnaireDAO 创建问卷DAO实例
func NewQuestionnaireDAO(conn sqlx.SqlConn) *QuestionnaireDAO {
	return &QuestionnaireDAO{
		questionnaireModel: NewQuestionnaireModel(conn),
	}
}

// QuestionnaireListRequest 问卷列表查询请求
type QuestionnaireListRequest struct {
	Page      int64  `json:"page"`       // 页码，从1开始
	PageSize  int64  `json:"pageSize"`  // 每页大小
	StartDate string `json:"startDate"` // 开始日期 YYYY-MM-DD
	EndDate   string `json:"endDate"`   // 结束日期 YYYY-MM-DD
}

// QuestionnaireListResponse 问卷列表查询响应
type QuestionnaireListResponse struct {
	Total int64                         `json:"total"`
	List  []*QuestionnaireWithPlatforms `json:"list"`
}

// QuestionnaireWithPlatforms 包含解析后平台信息的问卷
type QuestionnaireWithPlatforms struct {
	*Questionnaire
	PlatformList []string `json:"platformList"`
}

func (dao *QuestionnaireDAO) CountQuestionnaires(ctx context.Context) (int64, error) {
	// 使用FindCount方法统计问卷总数
	return dao.questionnaireModel.Count(ctx)
}

// CreateQuestionnaire 创建问卷记录
func (dao *QuestionnaireDAO) CreateQuestionnaire(ctx context.Context, req *CreateQuestionnaireRequest) (*Questionnaire, error) {
	// 将回答内容转换为JSON
	answersJSON, err := json.Marshal(req.Answers)
	if err != nil {
		return nil, fmt.Errorf("marshal answers failed: %w", err)
	}

	now := time.Now()
	questionnaire := &Questionnaire{
		RequestId:         req.RequestID,
		QuestionnaireName: req.QuestionnaireName,
		SubmitterEmail:    req.SubmitterEmail,
		Answers:           string(answersJSON),
		Status:            1, // 默认正常状态
		IsDeleted:         0,
		CreateTime:        now,
		UpdateTime:        now,
	}

	result, err := dao.questionnaireModel.Insert(ctx, questionnaire)
	if err != nil {
		return nil, fmt.Errorf("insert questionnaire failed: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("get last insert id failed: %w", err)
	}

	questionnaire.Id = id

	return questionnaire, nil
}

// CreateQuestionnaireRequest 创建问卷请求
type CreateQuestionnaireRequest struct {
	RequestID         string                 `json:"requestId"`
	QuestionnaireName string                 `json:"questionnaireName"`
	SubmitterEmail    string                 `json:"submitterEmail"`
	Answers           map[string]interface{} `json:"answers"`
}

// GetQuestionnaireByID 根据ID获取问卷
func (dao *QuestionnaireDAO) GetQuestionnaireByID(ctx context.Context, id int64) (*QuestionnaireWithPlatforms, error) {
	questionnaire, err := dao.questionnaireModel.FindOne(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("find questionnaire by id failed: %w", err)
	}

	return dao.convertToQuestionnaireWithAnswers(questionnaire)
}

// GetQuestionnaireByRequestID 根据请求ID获取问卷
func (dao *QuestionnaireDAO) GetQuestionnaireByRequestID(ctx context.Context, requestID string) (*QuestionnaireWithPlatforms, error) {
	questionnaire, err := dao.questionnaireModel.FindOneByRequestId(ctx, requestID)
	if err != nil {
		return nil, fmt.Errorf("find questionnaire by request id failed: %w", err)
	}

	return dao.convertToQuestionnaireWithAnswers(questionnaire)
}

// ListQuestionnaires 分页查询问卷列表
// 注意：此方法需要在customQuestionnaireModel中实现复杂查询
func (dao *QuestionnaireDAO) ListQuestionnaires(ctx context.Context, req *QuestionnaireListRequest) (*QuestionnaireListResponse, error) {
	return nil, fmt.Errorf("ListQuestionnaires method not implemented - please add to customQuestionnaireModel")
}

// UpdateQuestionnaire 更新问卷
func (dao *QuestionnaireDAO) UpdateQuestionnaire(ctx context.Context, questionnaire *Questionnaire) error {
	err := dao.questionnaireModel.Update(ctx, questionnaire)
	if err != nil {
		return fmt.Errorf("update questionnaire failed: %w", err)
	}
	return nil
}

// DeleteQuestionnaire 软删除问卷
func (dao *QuestionnaireDAO) DeleteQuestionnaire(ctx context.Context, id int64) error {
	questionnaire, err := dao.questionnaireModel.FindOne(ctx, id)
	if err != nil {
		return fmt.Errorf("find questionnaire failed: %w", err)
	}

	questionnaire.IsDeleted = 1
	questionnaire.Status = 0

	err = dao.questionnaireModel.Update(ctx, questionnaire)
	if err != nil {
		return fmt.Errorf("soft delete questionnaire failed: %w", err)
	}

	return nil
}

// convertToQuestionnaireWithAnswers 转换为包含回答内容的问卷结构
func (dao *QuestionnaireDAO) convertToQuestionnaireWithAnswers(q *Questionnaire) (*QuestionnaireWithPlatforms, error) {
	var answers map[string]interface{}
	if err := json.Unmarshal([]byte(q.Answers), &answers); err != nil {
		return nil, fmt.Errorf("unmarshal answers failed: %w", err)
	}

	return &QuestionnaireWithPlatforms{
		Questionnaire: q,
		PlatformList:  []string{}, // 保持兼容性，实际不再使用
	}, nil
}
