package domain

import (
	"time"

	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
)

// RebookingOption 重预订选项
type RebookingOption struct {
	Supplier        supplierDomain.Supplier
	RatePkgId       string
	NewPrice        int64 // 新价格（分）
	OriginalPrice   int64 // 原价格（分）
	PriceDifference int64 // 价格差异（负数表示节省）
	Room            supplierDomain.Room
}

// CancelAuditEvent 取消操作审计事件
type CancelAuditEvent struct {
	OrderID               types.ID               `json:"orderId"`
	Action                string                 `json:"action"`
	Timestamp             time.Time              `json:"timestamp"`
	UserID                types.ID               `json:"userId,omitempty"`
	OriginalStatus        OrderStatus            `json:"originalStatus"`
	FinalStatus           OrderStatus            `json:"finalStatus"`
	SupplierCancelSuccess bool                   `json:"supplierCancelSuccess"`
	SupplierErrors        []string               `json:"supplierErrors,omitempty"`
	Reason                string                 `json:"reason"`
	Metadata              map[string]interface{} `json:"metadata,omitempty"`
}

// CancelMetrics 取消操作监控指标
type CancelMetrics struct {
	TotalCancelRequests     int64 `json:"totalCancelRequests"`
	SuccessfulCancellations int64 `json:"successfulCancellations"`
	FailedCancellations     int64 `json:"failedCancellations"`
	SupplierCancelFailures  int64 `json:"supplierCancelFailures"`
	DatabaseUpdateFailures  int64 `json:"databaseUpdateFailures"`
	MessageQueueFailures    int64 `json:"messageQueueFailures"`
	AverageProcessingTimeMs int64 `json:"averageProcessingTimeMs"`
}
