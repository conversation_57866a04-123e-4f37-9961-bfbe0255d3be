package domain

import (
	"time"

	"hotel/common/money"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

// Order represents a hotel booking order in the trade system
// It contains all essential information about a customer's hotel reservation
// including booking details, payment information, and current status
type Order struct {
	Id            types.ID              `json:"id" required:"true"`                    // Unique identifier for the order
	ReferenceNo   string                `json:"referenceNo,omitempty"`                 // External reference number for tracking
	Status        OrderStatus           `json:"status" required:"true"`                // Current status of the order (created, confirmed, cancelled, etc.)
	CreateTime    time.Time             `json:"createTime,omitzero"`                   // Timestamp when the order was created
	ConfirmNumber string                `json:"confirmNumber,omitempty"`               // Hotel confirmation number from supplier
	Rooms         []OrderRoom           `json:"orderRoom,omitzero"`                    // List of rooms in this order
	Booker        supplierDomain.Booker `json:"booker,omitzero"`                       // Person who made the booking
	Tags          []string              `json:"tags,omitzero"`                         // Additional tags for categorization
	OrderAccount  *OrderAccount         `json:"orderAccount,omitempty" apidoc:"HotelCode"` // Account and payment information
	BizInfo       *OrderBizInfo         `json:"bizInfo,omitempty" apidoc:"HotelCode"`     // Business-specific information and metadata
}

// OrderSummary represents a condensed view of an order for list displays
// It contains key information needed for order listings and summaries
type OrderSummary struct {
	Id             types.ID                      `json:"id" required:"true"`           // Unique identifier for the order
	ReferenceNo    string                        `json:"referenceNo,omitempty"`        // External reference number for tracking
	Status         OrderStatus                   `json:"status" required:"true"`       // Current status of the order
	CreateTime     time.Time                     `json:"createTime,omitzero"`          // Timestamp when the order was created
	Nights         int64                         `json:"rooms,omitzero"`               // Number of nights for the stay
	Rooms          int64                         `json:"orderRoom,omitzero"`           // Number of rooms in the order
	Tags           []string                      `json:"tags,omitzero"`                // Additional tags for categorization
	SalesMoney     money.Money                   `json:"salesMoney,omitzero"`          // Total sales amount for the order
	Supplier       supplierDomain.Supplier       `json:"supplier,omitzero"`            // Supplier information for this order
	SupplierNet    money.Money                   `json:"supplierNet,omitzero"`         // Net amount paid to supplier
	RefundableMode supplierDomain.RefundableMode `json:"refundableMode,omitzero"`     // Refund policy mode for this order
}

// OrderGainSummary represents financial gain and rebooking information for an order
// It tracks revenue, refunds, and rebooking relationships
type OrderGainSummary struct {
	Gain                     money.Money `json:"gain,omitzero"`                     // Total profit/gain from this order
	Refunded                 money.Money `json:"refunded,omitzero"`                 // Amount that has been refunded
	AvailableRebookingBefore time.Time   `json:"availableRebookingBefore,omitzero"` // Deadline for rebooking this order
	RebookingOrderIdTo       int64       `json:"rebookingOrderIdTo,omitzero"`       // ID of the order this was rebooked to
	RebookingOrderIdFrom     int64       `json:"rebookingOrderIdFrom,omitzero"`     // ID of the order this was rebooked from
}

// OrderAccount represents account and entity information associated with an order
// It links the order to customer accounts and business entities
type OrderAccount struct {
	CustomerAccount   *userDomain.UserBasic `json:"customerAccount,omitempty"`   // Basic information of the customer who placed the order
	TenantBrandEntity *userDomain.Entity    `json:"tenantBrandEntity,omitempty"` // Tenant brand entity handling this order
	CustomerEntity    *userDomain.Entity    `json:"customerEntity,omitempty"`    // Customer's business entity (if applicable)
}

// OrderRoom represents a single room within an order
// It contains guest information and refund details for the room
type OrderRoom struct {
	RoomIndex  int64                   `json:"roomIndex" required:"true"`     // Index/number of the room within the order
	Guests     []*supplierDomain.Guest `json:"guest,omitempty" required:"true"` // List of guests staying in this room
	RefundInfo []*OrderRoomRefundInfo  `json:"refundInfo,omitzero"`            // Refund information for this specific room
}

// OrderRoomRefundInfo represents refund information for a specific room on a specific date
// It tracks whether a refund has been processed and the amount refunded
type OrderRoomRefundInfo struct {
	Date          types.DateInt `json:"date" required:"true"`          // Date for which the refund applies
	Refunded      bool          `json:"refunded" required:"true"`      // Whether the refund has been processed
	RefundedMoney money.Money   `json:"refundedMoney" required:"true"` // Amount of money refunded for this date
}

// PayOrderReq represents a request to pay for an order
// It contains payment details including currency and pricing breakdown
type PayOrderReq struct {
	OrderID       int64           `json:"orderId"`        // ID of the order to be paid
	Currency      string          `json:"currency"`        // Currency code for the payment (e.g., USD, EUR)
	TotalPrice    int64           `json:"totalPrice"`     // Total amount the user actually pays
	SubOrderPrice map[int64]int64 `json:"subOrderPrice"` // Price breakdown by sub-order ID
}

// PayOrderResp represents the response after processing an order payment
// It indicates whether the payment was successful
type PayOrderResp struct {
	Success bool `json:"success"` // Whether the payment was processed successfully
}

// OrderStatus represents the current state of an order in the booking lifecycle
// It tracks the order from creation through completion or cancellation
type OrderStatus int64

// Order status constants defining the various states an order can be in
const (
	OrderStateCreated               OrderStatus = 1 // Order has been created but not yet paid
	OrderStatePaid                  OrderStatus = 2 // Payment has been processed successfully
	OrderStateNeedSupplierConfirmed OrderStatus = 3 // Waiting for supplier confirmation
	OrderStateConfirmed             OrderStatus = 4 // Supplier has confirmed the booking
	OrderStateCompleted             OrderStatus = 5 // Guest has completed their stay
	OrderStateCancelled             OrderStatus = 6 // Order has been cancelled
	OrderStateNeedCancel            OrderStatus = 7 // Order is pending cancellation (used during rebooking)
	OrderStateNeedRefund            OrderStatus = 8 // Order is pending refund processing (used during rebooking)
)

// ConvertSupplierOrderStatusToTradeStatus converts a supplier's order status to the trade system's order status
// This function maps external supplier statuses to internal trade order statuses for consistency
func ConvertSupplierOrderStatusToTradeStatus(supplierStatus supplierDomain.OrderStatus) OrderStatus {
	switch supplierStatus {
	case supplierDomain.OrderStatus_Submitted:
		return OrderStatePaid
	case supplierDomain.OrderStatus_Confirming:
		return OrderStateNeedSupplierConfirmed
	case supplierDomain.OrderStatus_Confirmed:
		return OrderStateConfirmed
	case supplierDomain.OrderStatus_Cancelled:
		return OrderStateCancelled
	default:
		return OrderStateCreated
	}
}

func (s OrderStatus) String() string {
	n, _ := StateName(s)
	return n
}

func (s OrderStatus) Int64() int64 {
	return int64(s)
}
