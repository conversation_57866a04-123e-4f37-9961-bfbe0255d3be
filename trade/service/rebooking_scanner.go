package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"hotel/common/log"
	"hotel/common/types"
	"hotel/supplier"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/domain"
)

// RebookingScanner 重预订扫描器
type RebookingScanner struct {
	tradeService    *TradeService
	supplierFactory *supplier.Factory
	scanInterval    time.Duration // 扫描间隔
	stopChan        chan struct{}
	wg              sync.WaitGroup
}

func NewRebookingScanner(tradeService *TradeService, scanInterval time.Duration) *RebookingScanner {
	return &RebookingScanner{
		tradeService:    tradeService,
		supplierFactory: tradeService.supplier,
		scanInterval:    scanInterval,
		stopChan:        make(chan struct{}),
	}
}

func (r *RebookingScanner) Start(ctx context.Context) error {
	log.Info("Starting rebooking scanner...")
	r.wg.Add(1)
	go r.scanLoop(ctx)
	return nil
}

func (r *RebookingScanner) Stop() {
	log.Info("Stopping rebooking scanner...")
	close(r.stop<PERSON>han)
	r.wg.Wait()
	log.Info("Rebooking scanner stopped")
}

func (r *RebookingScanner) scanLoop(ctx context.Context) {
	defer r.wg.Done()
	ticker := time.NewTicker(r.scanInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := r.scanAndRebook(ctx); err != nil {
				log.Errorc(ctx, "Rebooking scan failed: %v", err)
			}
		case <-r.stopChan:
			return
		}
	}
}

func (r *RebookingScanner) scanAndRebook(ctx context.Context) error {
	// 查找符合重预订条件的订单
	eligibleOrders, err := r.findEligibleOrders(ctx)
	if err != nil {
		return fmt.Errorf("failed to find eligible orders: %w", err)
	}

	log.Infoc(ctx, "Found %d eligible orders for rebooking", len(eligibleOrders))

	// 并发处理每个订单
	for _, order := range eligibleOrders {
		r.wg.Add(1)
		go func(o domain.Order) {
			defer r.wg.Done()
			if err := r.processOrderRebooking(ctx, o); err != nil {
				log.Errorc(ctx, "Failed to process rebooking for order %d: %v", o.Id, err)
			}
		}(order)
	}

	return nil
}

// findEligibleOrders 查找符合重预订条件的订单
func (r *RebookingScanner) findEligibleOrders(ctx context.Context) ([]domain.Order, error) {
	// 查询条件：
	// 1. 订单状态为已确认(confirmed)
	// 2. 入住时间还未到
	// 3. 在免费取消时间窗口内
	// 4. 未标记为禁用重预订

	// 这里暂时查询所有已确认的订单，实际应该根据具体条件过滤
	orders, err := r.tradeService.orderDao.FindList(ctx, int(domain.OrderStateConfirmed))
	if err != nil {
		return nil, fmt.Errorf("failed to query orders: %w", err)
	}

	var eligibleOrders []domain.Order
	now := time.Now()

	for _, order := range orders {
		// 检查入住时间是否还未到
		// 这里需要从 order.BizInfo 中提取入住时间
		checkInDate, err := r.extractCheckInDateFromOrder(*order)
		if err != nil {
			log.Warnc(ctx, "Invalid check-in date for order %d: %v", order.Id, err)
			continue
		}

		// 如果入住时间已过，跳过
		if checkInDate.Before(now) {
			continue
		}

		// 检查是否在可重预订时间窗口内（这里需要根据订单的取消政策判断）
		if r.isWithinRebookingWindow(ctx, *order) {
			eligibleOrders = append(eligibleOrders, *order)
		}
	}

	return eligibleOrders, nil
}

// isWithinRebookingWindow 检查是否在重预订时间窗口内
func (r *RebookingScanner) isWithinRebookingWindow(ctx context.Context, order domain.Order) bool {
	// 这里需要根据订单的取消政策来判断
	// 简化实现：假设入住前48小时内可以重预订
	checkInDate, err := r.extractCheckInDateFromOrder(order)
	if err != nil {
		return false
	}

	rebookingDeadline := checkInDate.Add(-48 * time.Hour)
	return time.Now().Before(rebookingDeadline)
}

// extractCheckInDateFromOrder 从订单中提取入住日期
func (r *RebookingScanner) extractCheckInDateFromOrder(order domain.Order) (time.Time, error) {
	// 从 order.BizInfo 中提取入住时间
	if order.BizInfo == nil || order.BizInfo.CheckAvailResp == nil {
		return time.Time{}, fmt.Errorf("no check-in date available")
	}

	checkInDate := order.BizInfo.CheckAvailResp.RoomRatePkg.CheckIn
	dateStr := checkInDate.Format("2006-01-02")
	return time.Parse("2006-01-02", dateStr)
}

// processOrderRebooking 处理单个订单的重预订
func (r *RebookingScanner) processOrderRebooking(ctx context.Context, order domain.Order) error {
	log.Infoc(ctx, "Processing rebooking for order %d", order.Id)

	// 1. 搜索更好的房型选择
	betterOptions, err := r.searchBetterOptions(ctx, order)
	if err != nil {
		return fmt.Errorf("failed to search better options: %w", err)
	}

	if len(betterOptions) == 0 {
		log.Debugc(ctx, "No better options found for order %d", order.Id)
		return nil
	}

	// 2. 选择最佳选项
	bestOption := r.selectBestOption(betterOptions)
	if bestOption == nil {
		return nil
	}

	log.Infoc(ctx, "Found better option for order %d: supplier=%s, savings=%d",
		order.Id, bestOption.Supplier, bestOption.PriceDifference)

	// 3. 执行重预订
	return r.executeRebooking(ctx, order, bestOption)
}

// searchBetterOptions 搜索更好的房型选择
func (r *RebookingScanner) searchBetterOptions(ctx context.Context, order domain.Order) ([]*domain.RebookingOption, error) {
	// 构建搜索请求
	searchReq := &supplierDomain.HotelRatesReq{
		SupplierHotelId: extractHotelIdFromOrder(order),
		CheckInOut: supplierDomain.CheckInOut{
			CheckIn:  extractCheckInDateFromOrder(order),
			CheckOut: extractCheckOutDateFromOrder(order),
		},
		GuestRoomOption: extractGuestRoomOptionFromOrder(order),
		HotelRegion:     extractHotelRegionFromOrder(order),
	}

	var allOptions []*domain.RebookingOption

	// 从所有可用的供应商搜索
	suppliers := r.supplierFactory.GetAllSuppliers()
	for _, supplier := range suppliers {
		options, err := r.searchSupplierOptions(ctx, supplier, searchReq, order)
		if err != nil {
			log.Warnc(ctx, "Failed to search options from supplier %s: %v", supplier, err)
			continue
		}
		allOptions = append(allOptions, options...)
	}

	// 过滤出更好的选项
	return r.filterBetterOptions(allOptions, order), nil
}

// searchSupplierOptions 从单个供应商搜索选项
func (r *RebookingScanner) searchSupplierOptions(ctx context.Context, supplier supplier.Supplier,
	req *supplierDomain.HotelRatesReq, order domain.Order) ([]*domain.RebookingOption, error) {

	resp, err := supplier.HotelRates(ctx, req)
	if err != nil {
		return nil, err
	}

	var options []*domain.RebookingOption
	for _, room := range resp.Rooms {
		for _, rate := range room.Rates {
			option := &domain.RebookingOption{
				Supplier:        supplier.Supplier(),
				RatePkgId:       rate.RatePkgId,
				NewPrice:        int64(rate.Rate.FinalRate.Amount),
				OriginalPrice:   0, // 将在 filterBetterOptions 中设置
				PriceDifference: 0, // 将在 filterBetterOptions 中设置
				Room:            room,
			}
			options = append(options, option)
		}
	}

	return options, nil
}

// filterBetterOptions 过滤出更好的选项
func (r *RebookingScanner) filterBetterOptions(options []*domain.RebookingOption, order domain.Order) []*domain.RebookingOption {
	var betterOptions []*domain.RebookingOption

	// 从 order.BizInfo 中提取原价格信息
	originalPrice := r.extractOriginalPriceFromOrder(order)

	for _, option := range options {
		// 只选择价格更低的选项
		if option.NewPrice < originalPrice {
			option.OriginalPrice = originalPrice
			option.PriceDifference = option.NewPrice - originalPrice
			betterOptions = append(betterOptions, option)
		}
	}

	return betterOptions
}

// extractOriginalPriceFromOrder 从订单中提取原价格
func (r *RebookingScanner) extractOriginalPriceFromOrder(order domain.Order) int64 {
	// 从 order.BizInfo 中提取价格信息
	// 这里需要根据实际的 BizInfo 结构来实现
	// 暂时返回一个默认值
	return 10000 // 默认100元
}

// selectBestOption 选择最佳选项
func (r *RebookingScanner) selectBestOption(options []*domain.RebookingOption) *domain.RebookingOption {
	if len(options) == 0 {
		return nil
	}

	// 选择价格最低的选项
	bestOption := options[0]
	for _, option := range options[1:] {
		if option.NewPrice < bestOption.NewPrice {
			bestOption = option
		}
	}

	return bestOption
}

// executeRebooking 执行重预订
func (r *RebookingScanner) executeRebooking(ctx context.Context, order domain.Order, option *domain.RebookingOption) error {
	log.Infoc(ctx, "Executing rebooking for order %d with supplier %s", order.Id, option.Supplier)

	// 1. 取消原订单
	supplierInstance := r.supplierFactory.GetSupplier(option.Supplier)
	if supplierInstance == nil {
		return fmt.Errorf("supplier %s not found", option.Supplier)
	}

	if err := r.cancelOriginalOrder(ctx, order, supplierInstance); err != nil {
		return fmt.Errorf("failed to cancel original order: %w", err)
	}

	// 2. 创建新订单（这里需要实现预订逻辑）
	newSupplierOrderId := fmt.Sprintf("REBOOK_%d", order.Id)
	if err := r.updateOrderAfterRebooking(ctx, order, option, newSupplierOrderId); err != nil {
		return fmt.Errorf("failed to update order after rebooking: %w", err)
	}

	log.Infoc(ctx, "Successfully rebooked order %d", order.Id)
	return nil
}

// cancelOriginalOrder 取消原订单
func (r *RebookingScanner) cancelOriginalOrder(ctx context.Context, order domain.Order, supplier supplier.Supplier) error {
	// 这里需要获取原订单的supplier order id
	// 简化实现，假设存储在order的某个字段中
	supplierOrderId := extractSupplierOrderIdFromOrder(order)
	if supplierOrderId == "" {
		return fmt.Errorf("no supplier order id found for order %d", order.Id)
	}

	// 调用供应商取消接口
	cancelReq := &supplierDomain.CancelReq{
		SupplierOrderId: supplierOrderId,
	}

	_, err := supplier.Cancel(ctx, cancelReq)
	return err
}

// updateOrderAfterRebooking 重预订后更新订单
func (r *RebookingScanner) updateOrderAfterRebooking(ctx context.Context, order domain.Order,
	option *domain.RebookingOption, newSupplierOrderId string) error {

	// 更新订单信息
	order.Status = domain.OrderStateConfirmed
	// 这里需要更新其他字段，如价格、供应商信息等

	// 使用 dao 层的 Update 方法
	return r.tradeService.orderDao.Update(ctx, &order)
}

// Helper functions for extracting data from order
func extractHotelIdFromOrder(order domain.Order) string {
	// 这里需要从order中提取酒店ID，可能存储在BizInfo中
	if order.BizInfo != nil && order.BizInfo.CheckAvailResp != nil {
		// 从 CheckAvailResp 中提取酒店ID
		return ""
	}
	return ""
}

func extractCheckInDateFromOrder(order domain.Order) types.DateInt {
	// 从 order.BizInfo 中提取入住日期
	if order.BizInfo != nil && order.BizInfo.CheckAvailResp != nil {
		return order.BizInfo.CheckAvailResp.RoomRatePkg.CheckIn
	}
	return 0
}

func extractCheckOutDateFromOrder(order domain.Order) types.DateInt {
	// 从 order.BizInfo 中提取退房日期
	if order.BizInfo != nil && order.BizInfo.CheckAvailResp != nil {
		return order.BizInfo.CheckAvailResp.RoomRatePkg.CheckOut
	}
	return 0
}

func extractGuestRoomOptionFromOrder(order domain.Order) supplierDomain.GuestRoomOption {
	// 从订单中提取客房选项
	return supplierDomain.GuestRoomOption{}
}

func extractHotelRegionFromOrder(order domain.Order) supplierDomain.HotelRegion {
	// 从订单中提取酒店区域信息
	return supplierDomain.HotelRegion{}
}

func extractBookerFromOrder(order domain.Order) supplierDomain.Booker {
	// 从订单中提取预订人信息
	return order.Booker
}

func extractGuestsFromOrder(order domain.Order) []supplierDomain.Guest {
	// 从订单中提取客人信息
	if len(order.Rooms) > 0 {
		var guests []supplierDomain.Guest
		for _, guest := range order.Rooms[0].Guests {
			guests = append(guests, *guest)
		}
		return guests
	}
	return []supplierDomain.Guest{}
}

func extractSupplierOrderIdFromOrder(order domain.Order) string {
	// 从订单中提取供应商订单ID
	// 这里需要从 order.BizInfo 或其他字段中提取
	return ""
}
