package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"hotel/common/i18n"
	"hotel/common/log"
	"hotel/common/types"
	"hotel/supplier"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/domain"
	"hotel/trade/protocol"
)

// RebookingService 重预订服务
type RebookingService struct {
	tradeService    *TradeService
	supplierFactory *supplier.Factory
	scanner         *RebookingScanner
}

// NewRebookingService 创建重预订服务
func NewRebookingService(tradeService *TradeService) *RebookingService {
	service := &RebookingService{
		tradeService:    tradeService,
		supplierFactory: supplier.NewFactory(),
	}

	// 创建扫描器，默认每30分钟扫描一次
	service.scanner = NewRebookingScanner(tradeService, 30*time.Minute)

	return service
}

// StartScanner 启动重预订扫描器
func (s *RebookingService) StartScanner(ctx context.Context) error {
	return s.scanner.Start(ctx)
}

// StopScanner 停止重预订扫描器
func (s *RebookingService) StopScanner() {
	if s.scanner != nil {
		s.scanner.Stop()
	}
}

// ManualRebooking 手动触发单个订单的重预订
func (s *RebookingService) ManualRebooking(ctx context.Context, req *protocol.ManualRebookingReq) (*protocol.ManualRebookingResp, error) {
	log.Infoc(ctx, "Manual rebooking requested for order %s", req.OrderId)

	// 1. 查询订单
	orderId, err := strconv.ParseUint(req.OrderId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %w", err)
	}
	order, err := s.tradeService.orderDao.GetByID(ctx, types.ID(orderId))
	if err != nil {
		return nil, fmt.Errorf("order not found: %w", err)
	}

	// 2. 检查订单状态是否允许重预订
	if !s.isOrderEligibleForRebooking(order) {
		return &protocol.ManualRebookingResp{
			Success: false,
			Message: "Order is not eligible for rebooking",
		}, nil
	}

	// 3. 搜索更好的选项
	betterOptions, err := s.searchBetterOptionsForOrder(ctx, order)
	if err != nil {
		return nil, fmt.Errorf("failed to search better options: %w", err)
	}

	if len(betterOptions) == 0 {
		return &protocol.ManualRebookingResp{
			Success: false,
			Message: "No better options found",
		}, nil
	}

	// 4. 选择最佳选项
	bestOption := s.selectBestOption(betterOptions)
	if bestOption == nil {
		return &protocol.ManualRebookingResp{
			Success: false,
			Message: "No significant savings found",
		}, nil
	}

	// 5. 执行重预订
	err = s.executeRebookingForOrder(ctx, order, bestOption)
	if err != nil {
		return nil, fmt.Errorf("failed to execute rebooking: %w", err)
	}

	return &protocol.ManualRebookingResp{
		Success:       true,
		Message:       "Rebooking completed successfully",
		OriginalPrice: bestOption.OriginalPrice,
		NewPrice:      bestOption.NewPrice,
		Savings:       -bestOption.PriceDifference,
		NewSupplier:   bestOption.Supplier.String(),
		NewRatePkgId:  bestOption.RatePkgId,
	}, nil
}

// GetRebookingOptions 获取订单的重预订选项（不执行重预订）
func (s *RebookingService) GetRebookingOptions(ctx context.Context, req *protocol.GetRebookingOptionsReq) (*protocol.GetRebookingOptionsResp, error) {
	log.Infoc(ctx, "Getting rebooking options for order %s", req.OrderId)

	// 1. 查询订单
	orderId, err := strconv.ParseUint(req.OrderId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %w", err)
	}
	order, err := s.tradeService.orderDao.GetByID(ctx, types.ID(orderId))
	if err != nil {
		return nil, fmt.Errorf("order not found: %w", err)
	}

	// 2. 检查订单状态
	if !s.isOrderEligibleForRebooking(order) {
		return &protocol.GetRebookingOptionsResp{
			OrderId:  req.OrderId,
			Eligible: false,
			Message:  "Order is not eligible for rebooking",
			Options:  []protocol.RebookingOptionInfo{},
		}, nil
	}

	// 3. 搜索所有可用选项
	allOptions, err := s.searchBetterOptionsForOrder(ctx, order)
	if err != nil {
		return nil, fmt.Errorf("failed to search options: %w", err)
	}

	// 4. 转换为响应格式
	var optionInfos []protocol.RebookingOptionInfo
	for _, option := range allOptions {
		optionInfos = append(optionInfos, protocol.RebookingOptionInfo{
			Supplier:        option.Supplier.String(),
			RatePkgId:       option.RatePkgId,
			NewPrice:        option.NewPrice,
			OriginalPrice:   option.OriginalPrice,
			PriceDifference: option.PriceDifference,
			Savings:         -option.PriceDifference,
			RoomType:        getRoomNameString(option.Room.RoomName),
		})
	}

	return &protocol.GetRebookingOptionsResp{
		OrderId:  req.OrderId,
		Eligible: true,
		Message:  fmt.Sprintf("Found %d rebooking options", len(optionInfos)),
		Options:  optionInfos,
	}, nil
}

// GetRebookingHistory 获取订单的重预订历史
func (s *RebookingService) GetRebookingHistory(ctx context.Context, req *protocol.GetRebookingHistoryReq) (*protocol.GetRebookingHistoryResp, error) {
	// 1. 查询订单
	orderId, err := strconv.ParseUint(req.OrderId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %w", err)
	}
	order, err := s.tradeService.orderDao.GetByID(ctx, types.ID(orderId))
	if err != nil {
		return nil, fmt.Errorf("order not found: %w", err)
	}

	// 2. 解析重预订历史
	var bizInfoStr string
	if order.BizInfo != nil {
		bizInfoBytes, _ := json.Marshal(order.BizInfo)
		bizInfoStr = string(bizInfoBytes)
	}
	history := s.parseRebookingHistory(bizInfoStr)

	return &protocol.GetRebookingHistoryResp{
		OrderId: req.OrderId,
		History: history,
	}, nil
}

// SetRebookingConfig 设置重预订配置
func (s *RebookingService) SetRebookingConfig(ctx context.Context, req *protocol.SetRebookingConfigReq) error {
	// 这里可以设置重预订的配置参数，比如：
	// - 最小节省金额阈值
	// - 扫描间隔
	// - 启用/禁用特定供应商
	// - 时间窗口设置等

	log.Infoc(ctx, "Setting rebooking config: %+v", req)

	// TODO: 实现配置存储和应用逻辑

	return nil
}

// parseDate 解析日期字符串
func parseDate(dateStr string) (time.Time, error) {
	if dateStr == "" {
		return time.Time{}, fmt.Errorf("empty date string")
	}
	// 尝试多种日期格式
	formats := []string{
		"2006-01-02",
		"2006-01-02 15:04:05",
		"20060102",
	}
	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, nil
		}
	}
	return time.Time{}, fmt.Errorf("unable to parse date: %s", dateStr)
}

// isOrderEligibleForRebooking 检查订单是否符合重预订条件
func (s *RebookingService) isOrderEligibleForRebooking(order *domain.Order) bool {
	// 1. 订单状态必须是已确认
	if order.Status != domain.OrderStateConfirmed {
		return false
	}

	// 2. 从BizInfo中获取入住时间
	if order.BizInfo == nil || order.BizInfo.CheckAvailResp == nil || order.BizInfo.CheckAvailResp.RoomRatePkg == nil {
		return false
	}

	checkInDateStr := ""
	if order.BizInfo != nil && order.BizInfo.CheckAvailResp != nil && order.BizInfo.CheckAvailResp.RoomRatePkg != nil {
		checkInDateStr = order.BizInfo.CheckAvailResp.RoomRatePkg.CheckIn.Format("2006-01-02")
	}
	checkInDate, err := parseDate(checkInDateStr)
	if err != nil {
		return false
	}

	if checkInDate.Before(time.Now()) {
		return false
	}

	// 3. 在重预订时间窗口内
	rebookingDeadline := checkInDate.Add(-48 * time.Hour)
	if time.Now().After(rebookingDeadline) {
		return false
	}

	// 4. 检查是否被禁用重预订
	if s.isRebookingDisabled(order) {
		return false
	}

	return true
}

// isRebookingDisabled 检查订单是否被禁用重预订
func (s *RebookingService) isRebookingDisabled(order *domain.Order) bool {
	// 从BizInfo中检查是否有禁用标记
	if order.BizInfo == nil {
		return false
	}

	// 这里需要根据实际的BizInfo结构来实现
	// 暂时返回false，表示不禁用重预订
	return false
}

// searchBetterOptionsForOrder 为订单搜索更好的选项
func (s *RebookingService) searchBetterOptionsForOrder(ctx context.Context, order *domain.Order) ([]*domain.RebookingOption, error) {
	// 构建搜索请求
	searchReq := &supplierDomain.HotelRatesReq{
		SupplierHotelId: extractHotelIdFromOrder(*order),
		CheckInOut: supplierDomain.CheckInOut{
			CheckIn:  extractCheckInDateFromOrder(*order),
			CheckOut: extractCheckOutDateFromOrder(*order),
		},
		GuestRoomOption: extractGuestRoomOptionFromOrder(*order),
		HotelRegion:     extractHotelRegionFromOrder(*order),
	}

	var allOptions []*domain.RebookingOption

	// 并发查询所有供应商
	suppliers := s.supplierFactory.GetAllSuppliers()
	for _, sup := range suppliers {
		options, err := s.searchSupplierOptions(ctx, sup, searchReq, order)
		if err != nil {
			log.Warnc(ctx, "Failed to search options from supplier %s: %v", sup.Supplier(), err)
			continue
		}
		allOptions = append(allOptions, options...)
	}

	// 过滤出更好的选项
	return s.filterBetterOptions(allOptions, order), nil
}

// searchSupplierOptions 从单个供应商搜索选项
func (s *RebookingService) searchSupplierOptions(ctx context.Context, supplier supplier.Supplier,
	req *supplierDomain.HotelRatesReq, order *domain.Order) ([]*domain.RebookingOption, error) {

	resp, err := supplier.HotelRates(ctx, req)
	if err != nil {
		return nil, err
	}

	// 从BizInfo中获取原始价格
	originalPrice := int64(0)
	if order.BizInfo != nil && order.BizInfo.CheckAvailResp != nil && order.BizInfo.CheckAvailResp.RoomRatePkg != nil {
		originalPrice = int64(order.BizInfo.CheckAvailResp.RoomRatePkg.Rate.FinalRate.Amount)
	}

	var options []*domain.RebookingOption
	for _, room := range resp.Rooms {
		for _, rate := range room.Rates {
			newPrice := int64(rate.Rate.FinalRate.Amount)
			option := &domain.RebookingOption{
				Supplier:        supplier.Supplier(),
				RatePkgId:       rate.RatePkgId,
				NewPrice:        newPrice,
				OriginalPrice:   originalPrice,
				PriceDifference: newPrice - originalPrice,
				Room:            room,
			}
			options = append(options, option)
		}
	}

	return options, nil
}

// filterBetterOptions 过滤出更好的选项
func (s *RebookingService) filterBetterOptions(options []*domain.RebookingOption, order *domain.Order) []*domain.RebookingOption {
	var betterOptions []*domain.RebookingOption

	for _, option := range options {
		// 价格更低才考虑重预订
		if option.PriceDifference < 0 {
			betterOptions = append(betterOptions, option)
		}
	}

	return betterOptions
}

// selectBestOption 选择最佳选项
func (s *RebookingService) selectBestOption(options []*domain.RebookingOption) *domain.RebookingOption {
	if len(options) == 0 {
		return nil
	}

	// 选择价格最低的选项
	bestOption := options[0]
	for _, option := range options[1:] {
		if option.NewPrice < bestOption.NewPrice {
			bestOption = option
		}
	}

	// 只有节省金额达到一定阈值才执行重预订
	minSavings := int64(1000) // 10元
	if -bestOption.PriceDifference < minSavings {
		return nil
	}

	return bestOption
}

// executeRebookingForOrder 为订单执行重预订
func (s *RebookingService) executeRebookingForOrder(ctx context.Context, order *domain.Order, option *domain.RebookingOption) error {
	log.Infoc(ctx, "Executing rebooking for order %d with supplier %s", order.Id, option.Supplier)

	// 1. 先预订新的房型
	supplier := s.supplierFactory.GetSupplier(option.Supplier)
	if supplier == nil {
		return fmt.Errorf("supplier %s not found", option.Supplier)
	}

	bookReq := &supplierDomain.BookReq{
		RatePkgId:       option.RatePkgId,
		Booker:          extractBookerFromOrder(*order),
		Guests:          extractGuestsFromOrder(*order),
		Payment:         supplierDomain.Payment{},
		PlatformOrderId: int64(order.Id),
	}

	bookResp, err := supplier.Book(ctx, bookReq)
	if err != nil {
		return fmt.Errorf("failed to book new room: %w", err)
	}

	log.Infoc(ctx, "Successfully booked new room for order %d: supplier_order_id=%s",
		order.Id, bookResp.SupplierOrderId)

	// 2. 取消原订单
	if err := s.cancelOriginalOrder(ctx, order, supplier); err != nil {
		log.Errorc(ctx, "Failed to cancel original order %d: %v", order.Id, err)
		// 这里应该记录失败，可能需要人工干预
		return err
	}

	// 3. 更新订单信息
	return s.updateOrderAfterRebooking(ctx, order, option, bookResp.SupplierOrderId)
}

// cancelOriginalOrder 取消原订单
func (s *RebookingService) cancelOriginalOrder(ctx context.Context, order *domain.Order, supplier supplier.Supplier) error {
	originalSupplierOrderId := extractSupplierOrderIdFromOrder(*order)

	cancelReq := &supplierDomain.CancelReq{
		SupplierOrderId: originalSupplierOrderId,
		CancelType:      supplierDomain.CancelTypeAll,
		CancelReason:    "Rebooking to better option",
	}

	_, err := supplier.Cancel(ctx, cancelReq)
	return err
}

// updateOrderAfterRebooking 重预订后更新订单
func (s *RebookingService) updateOrderAfterRebooking(ctx context.Context, order *domain.Order,
	option *domain.RebookingOption, newSupplierOrderId string) error {

	// 更新订单的BizInfo中的价格信息
	if order.BizInfo != nil && order.BizInfo.CheckAvailResp != nil && order.BizInfo.CheckAvailResp.RoomRatePkg != nil {
		// 更新价格信息
		order.BizInfo.CheckAvailResp.RoomRatePkg.Rate.FinalRate.Amount = float64(option.NewPrice)
	}

	// 记录重预订信息到标签
	if order.BizInfo != nil {
		order.BizInfo.Label = append(order.BizInfo.Label, fmt.Sprintf("rebooking_executed_%s", time.Now().Format("20060102")))
	}

	// 使用DAO更新订单
	return s.tradeService.orderDao.Update(ctx, order)
}

// parseRebookingHistory 解析重预订历史
func (s *RebookingService) parseRebookingHistory(bizInfo string) []protocol.RebookingHistoryItem {
	history := make([]protocol.RebookingHistoryItem, 0)

	if bizInfo == "" {
		return history
	}

	var bizData map[string]interface{}
	if err := json.Unmarshal([]byte(bizInfo), &bizData); err != nil {
		return history
	}

	if rebookingData, ok := bizData["rebooking"].(map[string]interface{}); ok {
		if executedAt, ok := rebookingData["executed_at"].(string); ok {
			item := protocol.RebookingHistoryItem{
				ExecutedAt:      executedAt,
				OriginalPrice:   int64(rebookingData["original_price"].(float64)),
				NewPrice:        int64(rebookingData["new_price"].(float64)),
				Savings:         int64(rebookingData["savings"].(float64)),
				Supplier:        rebookingData["supplier"].(string),
				OriginalOrderId: rebookingData["original_supplier_order"].(string),
				NewOrderId:      rebookingData["new_supplier_order"].(string),
			}
			history = append(history, item)
		}
	}

	return history
}

// getRoomNameString 从I18N结构中获取房间名称字符串
func getRoomNameString(roomName i18n.I18N) string {
	if roomName.En != "" {
		return roomName.En
	}
	if roomName.Zh != "" {
		return roomName.Zh
	}
	if roomName.Ar != "" {
		return roomName.Ar
	}
	return "Unknown Room"
}
