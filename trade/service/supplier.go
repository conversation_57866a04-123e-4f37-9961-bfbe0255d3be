package service

import (
	"context"
	"errors"
	"fmt"

	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	"hotel/trade/domain"
)

// confirmSupplier：提交订单后，保持一定的轮训来确认我们的已提交订单 供应商是否真的确认订单
func (s *TradeService) confirmSupplier(ctx context.Context, daoOrder *dao.Order) error {
	domainOrder, err := s.orderDao.GetByID(ctx, types.ID(daoOrder.Id))
	if err != nil {
		return fmt.Errorf("order not found: %w", err)
	}

	if domainOrder.Status != domain.OrderStateCreated {
		return errors.New("order cannot be confirmed in current state")
	}

	domainOrder.Status = domain.OrderStatePaid
	// domainOrder.UpdateTime = time.Now() // domain.Order may not have UpdateTime field

	if err := s.orderDao.Update(ctx, domainOrder); err != nil {
		return fmt.Errorf("failed to confirm order: %w", err)
	}

	// 提交给供应商
	queryOrderReq := &supplierDomain.QueryOrderReq{
		// SupplierOrderId: order.SupplierOrderId,
	}
	supplierOrder, err := s.supplier.QueryOrder(ctx, queryOrderReq)
	if err != nil {
		return fmt.Errorf("failed to submit to supplier: %w", err)
	}

	if supplierOrder.Order.Basic.OrderStatus != supplierDomain.OrderStatus_Confirmed {
		domainOrder.Status = domain.ConvertSupplierOrderStatusToTradeStatus(supplierOrder.Order.Basic.OrderStatus)
		if err := s.orderDao.Update(ctx, domainOrder); err != nil {
			return fmt.Errorf("failed to confirm order: %w", err)
		}
	}
	return nil
}
