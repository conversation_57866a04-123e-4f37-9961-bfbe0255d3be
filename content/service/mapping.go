package service

import (
	"context"
	"hotel/common/httpdispatcher"
	"hotel/common/log"
	"hotel/common/pagehelper"
	"hotel/content/domain"
	"hotel/content/protocol"
)

// MappingService 映射服务
type MappingService struct {
	*deps
}

// NewMappingService 创建新的映射服务
func NewMappingService(deps *deps) *MappingService {
	return &MappingService{
		deps: deps,
	}
}

// Name 返回服务名称
func (s *MappingService) Name() string {
	return "content/mapping"
}

// GetChildren 返回子服务列表
func (s *MappingService) GetChildren() []httpdispatcher.Service {
	return nil
}

// StartReduceHotelCronJob 启动酒店匹配合并的后台任务，以减少重复酒店数据
func (s *MappingService) StartReduceHotelCronJob(ctx context.Context, req *protocol.StartReduceHotelCronJobReq) (*protocol.StartReduceHotelCronJobResp, error) {
	logid := log.GetLogidFromContext(ctx)
	pageReq := pagehelper.PageReq{PageNum: 1, PageSize: 100}
	for {
		hs, pageResp, err := s.dao.ListPage(ctx, nil, pageReq, req.Suppliers)
		if err != nil {
			return nil, err
		}
		pageReq.PageNum++
		for _, h := range hs {
			err := s.reduceHotel(ctx, h)
			if err != nil {
				return nil, err
			}
		}
		if !pageResp.HasMore {
			break
		}
	}

	return &protocol.StartReduceHotelCronJobResp{
		JobId: logid,
	}, nil
}

func (s *MappingService) reduceHotel(ctx context.Context, h *domain.Hotel) error {
	//todo: 酒店匹配合并
	return nil
}
