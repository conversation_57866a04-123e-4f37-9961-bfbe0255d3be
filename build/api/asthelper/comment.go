package asthelper

import (
	"go/ast"
	"os"
	"reflect"
	"runtime"
	"strings"

	"hotel/common/log"

	"hotel/common/utils"
)

// ExtractMethodCommentsByFineAndLine 从源文件提取方法前的注释
func ExtractMethodCommentsByFineAndLine(file string, line int) []string {
	// 1. 读取源文件
	srcBytes, err := os.ReadFile(file)
	if err != nil {
		log.Error("read file failed: %v, file(%s) line(%v)", err, file, line)
		return nil
	}

	// 2. 按行分割源码
	lines := strings.Split(string(srcBytes), "\n")
	if line > len(lines) || line < 1 {
		return nil
	}

	// 3. 向上查找注释块（支持单行注释和多行注释）
	var comments []string
	for i := line - 2; i >= 0; i-- { // line-2 因为数组从0开始且要检查方法声明前的行
		currentLine := strings.TrimSpace(lines[i])

		// 遇到非注释行则终止
		if !strings.HasPrefix(currentLine, "//") && !strings.HasPrefix(currentLine, "/*") {
			break
		}

		// 清理注释标记
		currentLine = strings.TrimPrefix(currentLine, "//")
		currentLine = strings.TrimPrefix(currentLine, "/*")
		currentLine = strings.TrimSuffix(currentLine, "*/")
		comments = append([]string{strings.TrimSpace(currentLine)}, comments...)
	}

	return comments
}

func ExtractTag(comments, tagName string) string {
	return ExtractTagFromComments(strings.Split(comments, "\n"), tagName)
}

func ExtractTagFromComments(comments []string, tagName string) string {
	for _, line := range comments {
		if strings.HasPrefix(line, "@"+tagName+":") {
			return strings.TrimSpace(strings.TrimPrefix(line, "@"+tagName+":"))
		}
	}
	return ""
}

// GetMethodSourceLocation 获取方法在源码中的位置
func GetMethodSourceLocation(method reflect.Method) (file string, line int) {
	pc := method.Func.Pointer()
	if pc == 0 {
		return "", 0
	}

	f := runtime.FuncForPC(pc)
	if f == nil {
		return "", 0
	}

	file, line = f.FileLine(pc)
	return file, line
}

// extractDescription 从注释中提取描述文本
func extractDescription(docs, comments *ast.CommentGroup) (out string) {
	ignores := []string{"@generic:"}
	d1, d2 := extractCommentGroupText(docs, ignores...), extractCommentGroupText(comments, ignores...)
	return strings.Join(utils.Filter([]string{strings.TrimSpace(d1), strings.TrimSpace(d2)}, func(s string) bool {
		return s != ""
	}), "\n")
}

// extractDescription 从注释中提取描述文本
func extractDescriptionRaw(docs, comments *ast.CommentGroup) (out string) {
	d1, d2 := extractCommentGroupText(docs), extractCommentGroupText(comments)
	return strings.Join(utils.Filter([]string{strings.TrimSpace(d1), strings.TrimSpace(d2)}, func(s string) bool {
		return s != ""
	}), "\n")
}

func extractTagValueFromDocComments(docs, comments *ast.CommentGroup, tagName string) (out []string) {
	desc := extractDescriptionRaw(docs, comments)
	return utils.Filter(strings.Split(ExtractTag(desc, tagName), ","), func(s string) bool {
		return s != ""
	})
}

// 提取并清理注释
func extractComments(commentGroup *ast.CommentGroup) string {
	return extractDescription(nil, commentGroup)
}

// 判断是否有omitempty标签
func hasOmitEmpty(tag *ast.BasicLit) bool {
	if tag == nil {
		return false
	}

	tagValue := strings.Trim(tag.Value, "`")
	jsonTag := reflect.StructTag(tagValue).Get("json")
	return strings.Contains(jsonTag, "omitempty")
}

// 从字段标签或注释中提取描述信息
// 优先级：1. 标签中的desc  2. 字段注释中的@desc
func extractFieldDesc(field *ast.Field) string {
	// 从结构体标签中提取desc
	if descFromTag := getDescFromTag(field); descFromTag != "" {
		return descFromTag
	}
	// 从注释中提取@desc
	return getDescFromField(field)
}

// 从结构体标签中解析desc标签内容
// 示例：`json:"id" desc:"用户ID"` → 返回"用户ID"
func getDescFromTag(field *ast.Field) string {
	return getValueFromTag(field, "desc")
}

func getValueFromTag(field *ast.Field, k string) string {
	if field.Tag == nil {
		return ""
	}
	tagStr := strings.Trim(field.Tag.Value, "`") // 去除反引号
	tag := reflect.StructTag(tagStr)
	return tag.Get(k) // 直接读取desc标签
}

func getDescFromField(field *ast.Field) string {
	if field.Doc == nil {
		return ""
	}
	for _, comment := range field.Doc.List {
		text := strings.TrimSpace(strings.TrimPrefix(comment.Text, "//"))
		if strings.HasPrefix(text, "@desc ") {
			return strings.TrimSpace(text[len("@desc "):])
		}
	}
	return ""
}

// 辅助函数：解析参数注释
func (m *Method) parseParamComment(doc string, identifier interface{}) *ParamSpec {
	// identifier可以是位置索引或字段名
	var target string
	switch v := identifier.(type) {
	case int:
		target = generateParamName(v)
	case string:
		target = v
	default:
		return nil
	}

	lines := strings.Split(doc, "\n")
	for _, line := range lines {
		if !strings.HasPrefix(line, "@param:") {
			continue
		}

		parts := strings.SplitN(line[len("@param:"):], " ", 4)
		if len(parts) < 4 {
			continue
		}

		if parts[1] != target {
			continue
		}

		return &ParamSpec{
			In:          parts[0],
			Name:        parts[1],
			Types:       m.parseStringType(parts[2]),
			Required:    parts[3] == "required",
			Description: strings.Trim(parts[4], `"`),
		}
	}
	return nil
}

func trimPrefix(c string, ignores ...string) string {
	for _, ignore := range ignores {
		// todo: plugin customize
		if v, ok := strings.CutPrefix(c, ignore); ok {
			// 分号前面的也挪掉
			vs := strings.SplitN(v, ";", 2)
			if len(vs) == 2 {
				return vs[1]
			}
			return v
		}
	}
	return c
}

// Text returns the text of the comment.
// Comment markers (//, /*, and */), the first space of a line comment, and
// leading and trailing empty lines are removed.
// Comment directives like "//line" and "//go:noinline" are also removed.
// Multiple empty lines are reduced to one, and trailing space on lines is trimmed.
// Unless the result is empty, it is newline-terminated.
func extractCommentGroupText(g *ast.CommentGroup, ignores ...string) string {
	if g == nil {
		return ""
	}
	comments := make([]string, len(g.List))
	for i, c := range g.List {
		comments[i] = c.Text
	}

	lines := make([]string, 0, 10) // most comments are less than 10 lines
	for _, c := range comments {
		// Remove comment markers.
		// The parser has given us exactly the comment text.
		switch c[1] {
		case '/':
			//-style comment (no newline at the end)
			c = c[2:]
			if len(c) == 0 {
				// empty line
				break
			}
			if c[0] == ' ' {
				// strip first space - required for Example tests
				c = c[1:]
				c = trimPrefix(c, ignores...) //added
				break
			}
			if isDirective(c) {
				// Ignore //go:noinline, //line, and so on.
				continue
			}
			c = trimPrefix(c, ignores...) //added
		case '*':
			/*-style comment */
			c = c[2 : len(c)-2]
		}

		// Split on newlines.
		cl := strings.Split(c, "\n")

		// Walk lines, stripping trailing white space and adding to list.
		for _, l := range cl {
			lines = append(lines, stripTrailingWhitespace(l))
		}
	}

	// Remove leading blank lines; convert runs of
	// interior blank lines to a single blank line.
	n := 0
	for _, line := range lines {
		if line != "" || n > 0 && lines[n-1] != "" {
			lines[n] = line
			n++
		}
	}
	lines = lines[0:n]

	// Add final "" entry to get trailing newline from Join.
	if n > 0 && lines[n-1] != "" {
		lines = append(lines, "")
	}

	return strings.Join(lines, "\n")
}

func isWhitespace(ch byte) bool { return ch == ' ' || ch == '\t' || ch == '\n' || ch == '\r' }

func stripTrailingWhitespace(s string) string {
	i := len(s)
	for i > 0 && isWhitespace(s[i-1]) {
		i--
	}
	return s[0:i]
}

// isDirective reports whether c is a comment directive.
// This code is also in go/printer.
func isDirective(c string) bool {
	// "//line " is a line directive.
	// "//extern " is for gccgo.
	// "//export " is for cgo.
	// (The // has been removed.)
	if strings.HasPrefix(c, "line ") || strings.HasPrefix(c, "extern ") || strings.HasPrefix(c, "export ") {
		return true
	}

	// "//[a-z0-9]+:[a-z0-9]"
	// (The // has been removed.)
	colon := strings.Index(c, ":")
	if colon <= 0 || colon+1 >= len(c) {
		return false
	}
	for i := 0; i <= colon+1; i++ {
		if i == colon {
			continue
		}
		b := c[i]
		if !('a' <= b && b <= 'z' || '0' <= b && b <= '9') {
			return false
		}
	}
	return true
}
